<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jnpf-java-boot</artifactId>
        <groupId>com.jnpf</groupId>
        <version>3.4.7.1-aqsoc-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jnpf-common-all</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-database</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-file-core-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jnpf</groupId>
            <artifactId>jnpf-common-auth</artifactId>
        </dependency>
    </dependencies>

</project>
