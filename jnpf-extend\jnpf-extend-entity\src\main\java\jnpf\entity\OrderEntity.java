package jnpf.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("ext_order")
public class OrderEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    /**
     * 客户Id
     */
    @TableField("F_CUSTOMERID")
    private String customerId;

    /**
     * 客户名称
     */
    @TableField("F_CUSTOMERNAME")
    private String customerName;

    /**
     * 业务员Id
     */
    @TableField("F_SALESMANID")
    private String salesmanId;

    /**
     * 业务员
     */
    @TableField("F_SALESMANNAME")
    private String salesmanName;

    /**
     * 订单日期
     */
    @TableField("F_ORDERDATE")
    private Date orderDate;

    /**
     * 订单编码
     */
    @TableField("F_ORDERCODE")
    private String orderCode;

    /**
     * 运输方式
     */
    @TableField("F_TRANSPORTMODE")
    private String transportMode;

    /**
     * 发货日期
     */
    @TableField("F_DELIVERYDATE")
    private Date deliveryDate;

    /**
     * 发货地址
     */
    @TableField("F_DELIVERYADDRESS")
    private String deliveryAddress;

    /**
     * 付款方式
     */
    @TableField("F_PAYMENTMODE")
    private String paymentMode;

    /**
     * 应收金额
     */
    @TableField("F_RECEIVABLEMONEY")
    private BigDecimal receivableMoney;

    /**
     * 定金比率
     */
    @TableField("F_EARNESTRATE")
    private BigDecimal earnestRate;

    /**
     * 预付定金
     */
    @TableField("F_PREPAYEARNEST")
    private BigDecimal prepayEarnest;

    /**
     * 当前状态
     */
    @TableField("F_CURRENTSTATE")
    private Integer currentState;

    /**
     * 流程引擎
     */
    @TableField("F_FLOWID")
    private String flowId;

    /**
     * 附件信息
     */
    @TableField("F_FILEJSON")
    private String fileJson;

}
