package jnpf.permission.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.Pagination;
import jnpf.base.vo.ListVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.constant.MsgCode;
import jnpf.permission.constant.PermissionConst;
import jnpf.permission.entity.OrganizeAdministratorEntity;
import jnpf.permission.entity.OrganizeEntity;
import jnpf.permission.model.organize.OrganizeModel;
import jnpf.permission.model.organizeadministrator.*;
import jnpf.permission.service.OrganizeAdministratorService;
import jnpf.permission.service.OrganizeService;
import jnpf.util.*;
import jnpf.util.treeutil.SumTree;
import jnpf.util.treeutil.newtreeutil.TreeDotUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 机构分级管理员
 *
 * <AUTHOR>
 * @版本： V3.1.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2021-08-30 10:30:04
 */
@Tag(name = "机构分级管理员", description = "organizeAdminIsTrator")
@Slf4j
@RestController
@RequestMapping("/api/permission/organizeAdminIsTrator")
public class OrganizeAdministratorController extends SuperController<OrganizeAdministratorService, OrganizeAdministratorEntity> {

    @Autowired
    private OrganizeAdministratorService organizeAdminIsTratorService;
    @Autowired
    private OrganizeService organizeService;
    @Autowired
    private UserProvider userProvider;

    /**
     * 获取分级管理员列表
     *
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "获取分级管理员列表")
    @SaCheckPermission(value = {"permission.grade"})
    @GetMapping
    public ActionResult<PageListVO<OrganizeAdministratorListVo>> list(Pagination pagination) {
        List<OrganizeAdministratorListVo> list = organizeAdminIsTratorService.getList(pagination);
        PaginationVO paginationVO = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
        return ActionResult.page(list, paginationVO);
    }

    /**
     * 保存分级管理员
     *
     * @param organizeAdminIsTratorCrForm 新建模型
     * @return
     */
    @Operation(summary = "保存分级管理员")
    @Parameters({
            @Parameter(name = "organizeAdminIsTratorCrForm", description = "新建模型", required = true)
    })
    @SaCheckPermission(value = {"permission.grade"})
    @PostMapping
    public ActionResult save(@RequestBody @Valid OrganizeAdminIsTratorCrForm organizeAdminIsTratorCrForm) {
        if (userProvider.get().getUserId().equals(organizeAdminIsTratorCrForm.getUserId())) {
            return ActionResult.fail("无法设置当前用户操作权限");
        }
        List<OrganizeAdministratorCrModel> list = new ArrayList<>(16);
        // 递归得到所有的数组
        getOrganizeAdminIsTratorModel(list, organizeAdminIsTratorCrForm.getOrgAdminModel());
//        // 处理转好的数据
//        getRealList(list);
        // 手动设置userId
        List<OrganizeAdministratorEntity> jsonToList = JsonUtil.getJsonToList(list, OrganizeAdministratorEntity.class);
        jsonToList.forEach(t -> t.setUserId(organizeAdminIsTratorCrForm.getUserId()));
        // 创建数据
        jsonToList.forEach(t -> organizeAdminIsTratorService.create(t));
        return ActionResult.success("保存成功");
    }

//    /**
//     * 对数据进行二次处理，处理子集权限
//     *
//     * @param list
//     */
//    private void getRealList(List<OrganizeAdministratorCrModel> list) {
//        // 定义一个list用来处理数据
//        List<OrganizeAdministratorCrModel> crModelList = new ArrayList<>(list);
//        // 有子集权限的需要给子集赋值
//        list.forEach(t -> {
//            // 做一个标记用来判断 查看--select 添加--add 编辑--edit 删除--delete
//            // 如果有子集查看权限
//            if (t.getSubLayerSelect() == 1) {
//                String flag = "_select_";
//                if (t.getSubLayerAdd() == 1) {
//                    flag += "_add_";
//                }
//                if (t.getSubLayerEdit() == 1) {
//                    flag += "_edit_";
//                }
//                if (t.getSubLayerDelete() == 1) {
//                    flag += "_delete_";
//                }
//                // 得到所有子集id
//                List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
//                String finalFlag = flag;
//                underOrganizations.forEach(un -> {
//                    OrganizeAdministratorCrModel organizeAdministratorCrModel = crModelList.stream().filter(crModel -> crModel.getOrganizeId().equals(un)).findFirst().orElse(null);
//                    if (organizeAdministratorCrModel != null) {
////                        if (finalFlag.contains("select")) {
//                        organizeAdministratorCrModel.setThisLayerSelect(1);
////                        }
//                        if (finalFlag.contains("add")) {
//                            organizeAdministratorCrModel.setThisLayerAdd(1);
//                        }
//                        if (finalFlag.contains("edit")) {
//                            organizeAdministratorCrModel.setThisLayerEdit(1);
//                        }
//                        if (finalFlag.contains("delete")) {
//                            organizeAdministratorCrModel.setThisLayerDelete(1);
//                        }
//                    }
//                });
//            }
//        });
//
//    }

    /**
     * 获取
     *
     * @param list
     * @param jsonToList
     */
    private void getOrganizeAdminIsTratorModel(List<OrganizeAdministratorCrModel> list, List<OrganizeAdministratorCrModel> jsonToList) {
        if (jsonToList != null) {
            jsonToList.forEach(t -> {
                OrganizeAdministratorCrModel vo = JsonUtil.getJsonToBean(t, OrganizeAdministratorCrModel.class);
                vo.setChildren(null);
                if (vo.getThisLayerSelect() != null && (vo.getThisLayerSelect() == 2 || vo.getThisLayerSelect() == 1)) {
                    vo.setThisLayerSelect(1);
                    if (vo.getThisLayerAdd() != null && (vo.getThisLayerAdd() == 2 || vo.getThisLayerAdd() == 1)) {
                        vo.setThisLayerAdd(1);
                    }
                    if (vo.getThisLayerEdit() != null && (vo.getThisLayerEdit() == 2 || vo.getThisLayerEdit() == 1)) {
                        vo.setThisLayerEdit(1);
                    }
                    if (vo.getThisLayerDelete() != null && (vo.getThisLayerDelete() == 2 || vo.getThisLayerDelete() == 1)) {
                        vo.setThisLayerDelete(1);
                    }
                } else if (vo.getThisLayerSelect() == null || vo.getThisLayerSelect() == 0 || vo.getThisLayerSelect() == 3) {
                    vo.setThisLayerSelect(0);
                    vo.setThisLayerAdd(0);
                    vo.setThisLayerEdit(0);
                    vo.setThisLayerDelete(0);
                }
                if (vo.getSubLayerSelect() != null && (vo.getSubLayerSelect() == 2 || vo.getSubLayerSelect() == 1)) {
                    vo.setSubLayerSelect(1);
                    if (vo.getSubLayerAdd() != null && (vo.getSubLayerAdd() == 2 || vo.getSubLayerAdd() == 1)) {
                        vo.setSubLayerAdd(1);
                    }
                    if (vo.getSubLayerEdit() != null && (vo.getSubLayerEdit() == 2 || vo.getSubLayerEdit() == 1)) {
                        vo.setSubLayerEdit(1);
                    }
                    if (vo.getSubLayerDelete() != null && (vo.getSubLayerDelete() == 2 || vo.getSubLayerDelete() == 1)) {
                        vo.setSubLayerDelete(1);
                    }
                } else if (vo.getSubLayerSelect() == null || vo.getSubLayerSelect() == 0 || vo.getSubLayerSelect() == 3) {
                    vo.setSubLayerSelect(0);
                    vo.setSubLayerAdd(0);
                    vo.setSubLayerEdit(0);
                    vo.setSubLayerDelete(0);
                }
                list.add(vo);
                getOrganizeAdminIsTratorModel(list, t.getChildren());
            });
        }
    }

    /**
     * 删除二级管理员
     *
     * @param userId 主键值
     * @return
     */
    @Operation(summary = "删除二级管理员")
    @Parameters({
            @Parameter(name = "id", description = "用户id", required = true)
    })
    @SaCheckPermission(value = {"permission.grade"})
    @DeleteMapping("/{id}")
    public ActionResult delete(@PathVariable("id") String userId) {
        organizeAdminIsTratorService.deleteByUserId(userId);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 获取组织下拉框列表
     *
     * @return
     */
    @Operation(summary = "获取组织下拉框列表")
    @SaCheckPermission(value = {"permission.grade"})
    @GetMapping("/Selector")
    public ActionResult getSelector(String userId) {
        // 存储组织集合
        List<OrganizeEntity> organizeList = organizeService.getList();
        // 获取所有组织
        List<OrganizeAdministratorSelectorModel> selectorModels = JsonUtil.getJsonToList(organizeList, OrganizeAdministratorSelectorModel.class);
        boolean isAdministrator = userProvider.get().getIsAdministrator();
        if (isAdministrator) {
            selectorModels.forEach(t -> {
                // 设置组织id
                t.setOrganizeId(t.getId());
                t.setThisLayerAdd(0);
                t.setThisLayerEdit(0);
                t.setThisLayerDelete(0);
                t.setThisLayerSelect(0);
                t.setSubLayerAdd(0);
                t.setSubLayerEdit(0);
                t.setSubLayerDelete(0);
                t.setSubLayerSelect(0);
            });
        } else {
            List<OrganizeAdministratorEntity> organizeAdministratorEntity = organizeAdminIsTratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
            List<OrganizeAdministratorEntity> organizeAdministratorEntitys = new ArrayList<>(organizeAdministratorEntity);
            // 处理子组织权限
            organizeAdministratorEntitys.forEach(t -> {
                if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                    // 得到组织id
                    List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                    underOrganizations.forEach(uo -> {
                        OrganizeAdministratorEntity organizeAdministratorEntity1 = organizeAdministratorEntity.stream().filter(oae -> uo.equals(oae.getOrganizeId())).findFirst().orElse(null);
                        if (organizeAdministratorEntity1 != null) {
                            organizeAdministratorEntity1.setThisLayerSelect(1);
                            organizeAdministratorEntity1.setSubLayerSelect(1);
                        }
                    });
                    if (t.getSubLayerAdd() != null && t.getSubLayerAdd() == 1) {
                        underOrganizations.forEach(uo -> {
                            OrganizeAdministratorEntity organizeAdministratorEntity1 = organizeAdministratorEntity.stream().filter(oae -> uo.equals(oae.getOrganizeId())).findFirst().orElse(null);
                            if (organizeAdministratorEntity1 != null) {
                                organizeAdministratorEntity1.setThisLayerAdd(1);
                                organizeAdministratorEntity1.setSubLayerAdd(1);
                            }
                        });
                    }
                    if (t.getSubLayerEdit() != null && t.getSubLayerEdit() == 1) {
                        underOrganizations.forEach(uo -> {
                            OrganizeAdministratorEntity organizeAdministratorEntity1 = organizeAdministratorEntity.stream().filter(oae -> uo.equals(oae.getOrganizeId())).findFirst().orElse(null);
                            if (organizeAdministratorEntity1 != null) {
                                organizeAdministratorEntity1.setThisLayerEdit(1);
                                organizeAdministratorEntity1.setSubLayerEdit(1);
                            }
                        });
                    }
                    if (t.getSubLayerDelete() != null && t.getSubLayerDelete() == 1) {
                        underOrganizations.forEach(uo -> {
                            OrganizeAdministratorEntity organizeAdministratorEntity1 = organizeAdministratorEntity.stream().filter(oae -> uo.equals(oae.getOrganizeId())).findFirst().orElse(null);
                            if (organizeAdministratorEntity1 != null) {
                                organizeAdministratorEntity1.setThisLayerDelete(1);
                                organizeAdministratorEntity1.setSubLayerDelete(1);
                            }
                        });
                    }
                }
            });

            List<OrganizeAdministratorSelectorModel> selectorModelss = new ArrayList<>(16);
            selectorModels.forEach(t -> {
                // 设置组织id
                t.setOrganizeId(t.getId());
                OrganizeAdministratorEntity administratorEntity = organizeAdministratorEntity.stream().filter(tt -> t.getOrganizeId().equals(tt.getOrganizeId())).findFirst().orElse(null);
                if (administratorEntity != null) {
                    boolean flag = false;
                    if (administratorEntity.getThisLayerSelect() != null && administratorEntity.getThisLayerSelect() == 1) {
                        t.setThisLayerSelect(0);
                        flag = true;
                        if (administratorEntity.getThisLayerAdd() == 1) {
                            t.setThisLayerAdd(0);
                        }
                        if (administratorEntity.getThisLayerEdit() == 1) {
                            t.setThisLayerEdit(0);
                        }
                        if (administratorEntity.getThisLayerDelete() == 1) {
                            t.setThisLayerDelete(0);
                        }
                    }
                    if (administratorEntity.getSubLayerSelect()!= null && administratorEntity.getSubLayerSelect() == 1) {
                        t.setSubLayerSelect(0);
                        flag = true;
                        if (administratorEntity.getSubLayerAdd()!= null && administratorEntity.getSubLayerAdd() == 1) {
                            t.setSubLayerAdd(0);
                        }
                        if (administratorEntity.getSubLayerEdit()!= null && administratorEntity.getSubLayerEdit() == 1) {
                            t.setSubLayerEdit(0);
                        }
                        if (administratorEntity.getSubLayerDelete()!= null && administratorEntity.getSubLayerDelete() == 1) {
                            t.setSubLayerDelete(0);
                        }
                    }
                    if (flag) {
                        selectorModelss.add(t);
                    }
                }
            });
            selectorModels = selectorModelss;
        }
        // 判断是否为空
        if (StringUtil.isNotEmpty(userId)) {
            List<OrganizeAdministratorEntity> organizeAdministratorEntity = organizeAdminIsTratorService.getOrganizeAdministratorEntity(userId);
            // 处理子组织字段
            List<OrganizeAdministratorSelectorModel> finalSelectorModels3 = selectorModels;
            organizeAdministratorEntity.forEach(t -> {
                if (!isAdministrator) {
                    if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                        // 得到组织id
                        List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                        // 将同样的组织id的数据先处理好
                        List<OrganizeAdministratorSelectorModel> collect1 = finalSelectorModels3.stream().filter(fsm -> underOrganizations.contains(fsm.getOrganizeId())).collect(Collectors.toList());
                        collect1.forEach(cl -> {
                            OrganizeAdministratorSelectorModel organizeAdministratorSelectorModel = finalSelectorModels3.stream().filter(fms -> fms.getOrganizeId().equals(cl.getOrganizeId())).findFirst().orElse(null);
                            if (organizeAdministratorSelectorModel != null) {
                                if (organizeAdministratorSelectorModel.getThisLayerSelect() != null && organizeAdministratorSelectorModel.getThisLayerSelect() == 0) {
                                    organizeAdministratorSelectorModel.setThisLayerSelect(0);
                                } else {
                                    organizeAdministratorSelectorModel.setThisLayerSelect(3);
                                }
                                if (organizeAdministratorSelectorModel.getSubLayerSelect() != null && organizeAdministratorSelectorModel.getSubLayerAdd() == 0) {
                                    organizeAdministratorSelectorModel.setSubLayerAdd(0);
                                } else {
                                    organizeAdministratorSelectorModel.setSubLayerSelect(3);
                                }
                                if (t.getSubLayerAdd() == 1) {
                                    if (organizeAdministratorSelectorModel.getThisLayerAdd() != null && organizeAdministratorSelectorModel.getThisLayerAdd() == 0) {
                                        organizeAdministratorSelectorModel.setThisLayerAdd(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setThisLayerAdd(3);
                                    }
                                    if (organizeAdministratorSelectorModel.getSubLayerAdd() != null && organizeAdministratorSelectorModel.getSubLayerAdd() == 0) {
                                        organizeAdministratorSelectorModel.setSubLayerAdd(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setSubLayerAdd(3);
                                    }
                                }

                                if (t.getSubLayerEdit() == 1) {
                                    if (organizeAdministratorSelectorModel.getThisLayerEdit() != null && organizeAdministratorSelectorModel.getThisLayerEdit() == 0) {
                                        organizeAdministratorSelectorModel.setThisLayerEdit(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setThisLayerEdit(3);
                                    }
                                    if (organizeAdministratorSelectorModel.getSubLayerEdit() != null && organizeAdministratorSelectorModel.getSubLayerEdit() == 0) {
                                        organizeAdministratorSelectorModel.setSubLayerEdit(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setSubLayerEdit(3);
                                    }
                                }

                                if (t.getSubLayerDelete() == 1) {
                                    if (organizeAdministratorSelectorModel.getThisLayerDelete() != null && organizeAdministratorSelectorModel.getThisLayerDelete() == 0) {
                                        organizeAdministratorSelectorModel.setThisLayerDelete(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setThisLayerDelete(3);
                                    }
                                    if (organizeAdministratorSelectorModel.getSubLayerDelete() != null && organizeAdministratorSelectorModel.getSubLayerDelete() == 0) {
                                        organizeAdministratorSelectorModel.setSubLayerDelete(0);
                                    } else {
                                        organizeAdministratorSelectorModel.setSubLayerDelete(3);
                                    }
                                }
                            }
                        });
                        // 当前模型包含组织id
                        List<String> collect = underOrganizations.stream().filter(uo -> !finalSelectorModels3.stream().map(OrganizeAdministratorSelectorModel::getOrganizeId).collect(Collectors.toList()).contains(uo)).collect(Collectors.toList());
                        collect.forEach(cl -> {
                            OrganizeAdministratorEntity organizeAdministratorEntity1 = organizeAdministratorEntity.stream().filter(oa -> oa.getOrganizeId().equals(cl)).findFirst().orElse(null);
                            if (organizeAdministratorEntity1 != null) {
                                OrganizeEntity info = organizeService.getInfo(organizeAdministratorEntity1.getOrganizeId());
                                OrganizeAdministratorSelectorModel organizeAdministratorSelectorModel = JsonUtil.getJsonToBean(info, OrganizeAdministratorSelectorModel.class);
                                organizeAdministratorSelectorModel.setOrganizeId(organizeAdministratorEntity1.getOrganizeId());
                                organizeAdministratorSelectorModel.setThisLayerSelect(3);
                                organizeAdministratorSelectorModel.setSubLayerSelect(3);

                                if (t.getSubLayerAdd() == 1) {
                                    organizeAdministratorSelectorModel.setThisLayerAdd(3);
                                    organizeAdministratorSelectorModel.setSubLayerAdd(3);
                                }

                                if (t.getSubLayerEdit() == 1) {
                                    organizeAdministratorSelectorModel.setThisLayerEdit(3);
                                    organizeAdministratorSelectorModel.setSubLayerEdit(3);
                                }

                                if (t.getSubLayerDelete() == 1) {
                                    organizeAdministratorSelectorModel.setThisLayerDelete(3);
                                    organizeAdministratorSelectorModel.setSubLayerDelete(3);
                                }
                                finalSelectorModels3.add(organizeAdministratorSelectorModel);
                            }
                        });
                    }
                }
            });
            List<OrganizeAdministratorSelectorModel> finalSelectorModels = new ArrayList<>(selectorModels);
            List<OrganizeAdministratorSelectorModel> finalSelectorModels1 = new ArrayList<>(selectorModels);
            organizeAdministratorEntity.forEach(t -> {
                // 我没有他有时，需要判断此组织是否跟我所管理的组织相同，不同则需要放进去
                OrganizeAdministratorSelectorModel organizeAdministratorSelectorModel = finalSelectorModels.stream().filter(selectorModel -> selectorModel.getOrganizeId().equals(t.getOrganizeId())).findFirst().orElse(null);
                if (organizeAdministratorSelectorModel == null) {
                    boolean flag = false;
                    if (t.getThisLayerSelect() != null) {
                        if (t.getThisLayerSelect() == 1) {
                            t.setThisLayerSelect(2);
                            flag = true;
                            if (flag && t.getThisLayerAdd() != null && t.getThisLayerAdd() == 1) {
                                t.setThisLayerAdd(2);
                            } else if (t.getThisLayerAdd() != null) {
                                t.setThisLayerAdd(null);
                            }
                            if (flag && t.getThisLayerEdit() != null && t.getThisLayerEdit() == 1) {
                                t.setThisLayerEdit(2);
                            } else if (t.getThisLayerEdit() != null) {
                                t.setThisLayerEdit(null);
                            }
                            if (flag && t.getThisLayerDelete() != null && t.getThisLayerDelete() == 1) {
                                t.setThisLayerDelete(2);
                            } else if (t.getThisLayerDelete() != null) {
                                t.setThisLayerDelete(null);
                            }
                        } else {
                            t.setThisLayerSelect(null);
                            t.setThisLayerAdd(null);
                            t.setThisLayerEdit(null);
                            t.setThisLayerDelete(null);
                        }
                    }
                    boolean flag1 = false;
                    if (t.getSubLayerSelect() != null) {
                        if (t.getSubLayerSelect() == 1) {
                            t.setSubLayerSelect(2);
                            flag1 = true;
                            if (flag1 && t.getSubLayerAdd() != null && t.getSubLayerAdd() == 1) {
                                t.setSubLayerAdd(2);
                            } else if (t.getSubLayerAdd() != null) {
                                t.setSubLayerAdd(null);
                            }
                            if (flag1 && t.getSubLayerEdit() != null && t.getSubLayerEdit() == 1) {
                                t.setSubLayerEdit(2);
                            } else if (t.getSubLayerEdit() != null) {
                                t.setSubLayerEdit(null);
                            }
                            if (flag1 && t.getSubLayerDelete() != null && t.getSubLayerDelete() == 1) {
                                t.setSubLayerDelete(2);
                            } else if (t.getSubLayerDelete() != null) {
                                t.setSubLayerDelete(null);
                            }
                        } else {
                            t.setSubLayerSelect(null);
                            t.setSubLayerAdd(null);
                            t.setSubLayerEdit(null);
                            t.setSubLayerDelete(null);
                        }
                    }
                    if (flag || flag1) {
                        OrganizeAdministratorSelectorModel jsonToBean = JsonUtil.getJsonToBean(t, OrganizeAdministratorSelectorModel.class);
                        OrganizeEntity info = organizeService.getInfo(t.getOrganizeId());
                        if (info != null) {
                            jsonToBean.setParentId(info.getParentId());
                            jsonToBean.setId(info.getId());
                            jsonToBean.setCategory(info.getCategory());
                            jsonToBean.setIcon(PermissionConst.COMPANY.equals(info.getCategory()) ? "icon-ym icon-ym-tree-organization3" : "icon-ym icon-ym-tree-department1");
                            jsonToBean.setOrganizeId(t.getOrganizeId());
                            jsonToBean.setOrganizeIdTree(info.getOrganizeIdTree());
                            jsonToBean.setFullName(info.getFullName());
                            finalSelectorModels1.add(jsonToBean);
                        }
                    }
                }
            });
            organizeAdministratorEntity.forEach(t -> {
                finalSelectorModels1.forEach(fs -> {
                    if (t.getOrganizeId().equals(fs.getOrganizeId())) {
                        // 本层添加
                        if (fs.getThisLayerAdd() != null) {
                            if (fs.getThisLayerAdd() == 0) {
                                if (t.getThisLayerAdd() != null) {
                                    if (t.getThisLayerAdd() == 1) {
                                        fs.setThisLayerAdd(1);
                                    } else {
                                        fs.setThisLayerAdd(0);
                                    }
                                }
                            } else if (fs.getThisLayerAdd() == 3) {
                                if (t.getThisLayerAdd() != null) {
                                    if (t.getThisLayerAdd() == 1) {
                                        fs.setThisLayerAdd(1);
                                    } else {
                                        fs.setThisLayerAdd(3);
                                    }
                                }
                            } else {
                                if (t.getThisLayerAdd() != null) {
                                    if (t.getThisLayerAdd() == 1) {
                                        if (isAdministrator) {
                                            fs.setThisLayerAdd(1);
                                        } else {
                                            fs.setThisLayerAdd(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getThisLayerAdd() != null) {
                                if (t.getThisLayerAdd() == 1) {
                                    if (isAdministrator) {
                                        fs.setThisLayerAdd(1);
                                    } else {
                                        fs.setThisLayerAdd(2);
                                    }
                                }
                            }
                        }
                        // 本层编辑
                        if (fs.getThisLayerEdit() != null) {
                            if (fs.getThisLayerEdit() == 0) {
                                if (t.getThisLayerEdit() != null) {
                                    if (t.getThisLayerEdit() == 1) {
                                        fs.setThisLayerEdit(1);
                                    } else {
                                        fs.setThisLayerEdit(0);
                                    }
                                }
                            } else if (fs.getThisLayerEdit() == 3) {
                                if (t.getThisLayerEdit() != null) {
                                    if (t.getThisLayerEdit() == 1) {
                                        fs.setThisLayerEdit(1);
                                    } else {
                                        fs.setThisLayerEdit(3);
                                    }
                                }
                            } else {
                                if (t.getThisLayerEdit() != null) {
                                    if (t.getThisLayerEdit() == 1) {
                                        if (isAdministrator) {
                                            fs.setThisLayerEdit(1);
                                        } else {
                                            fs.setThisLayerEdit(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getThisLayerEdit() != null) {
                                if (t.getThisLayerEdit() == 1) {
                                    if (isAdministrator) {
                                        fs.setThisLayerEdit(1);
                                    } else {
                                        fs.setThisLayerEdit(2);
                                    }
                                }
                            }
                        }
                        // 本层删除
                        if (fs.getThisLayerDelete() != null) {
                            if (fs.getThisLayerDelete() == 0) {
                                if (t.getThisLayerDelete() != null) {
                                    if (t.getThisLayerDelete() == 1) {
                                        fs.setThisLayerDelete(1);
                                    } else {
                                        fs.setThisLayerDelete(0);
                                    }
                                }
                            } else if (fs.getThisLayerDelete() == 3) {
                                if (t.getThisLayerDelete() != null) {
                                    if (t.getThisLayerDelete() == 1) {
                                        fs.setThisLayerDelete(1);
                                    } else {
                                        fs.setThisLayerDelete(3);
                                    }
                                }
                            } else {
                                if (t.getThisLayerDelete() != null) {
                                    if (t.getThisLayerDelete() == 1) {
                                        if (isAdministrator) {
                                            fs.setThisLayerDelete(1);
                                        } else {
                                            fs.setThisLayerDelete(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getThisLayerDelete() != null) {
                                if (t.getThisLayerDelete() == 1) {
                                    if (isAdministrator) {
                                        fs.setThisLayerDelete(1);
                                    } else {
                                        fs.setThisLayerDelete(2);
                                    }
                                }
                            }
                        }
                        // 本层查看
                        if (fs.getThisLayerSelect() != null) {
                            if (fs.getThisLayerSelect() == 0) {
                                if (t.getThisLayerSelect() != null) {
                                    if (t.getThisLayerSelect() == 1) {
                                        fs.setThisLayerSelect(1);
                                    } else {
                                        fs.setThisLayerSelect(0);
                                    }
                                }
                            } else if (fs.getThisLayerSelect() == 3) {
                                if (t.getThisLayerSelect() != null) {
                                    if (t.getThisLayerSelect() == 1) {
                                        fs.setThisLayerSelect(1);
                                    } else {
                                        fs.setThisLayerSelect(3);
                                    }
                                }
                            } else {
                                if (t.getThisLayerSelect() != null) {
                                    if (t.getThisLayerSelect() == 1) {
                                        if (isAdministrator) {
                                            fs.setThisLayerSelect(1);
                                        } else {
                                            fs.setThisLayerSelect(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getThisLayerSelect() != null) {
                                if (t.getThisLayerSelect() == 1) {
                                    if (isAdministrator) {
                                        fs.setThisLayerSelect(1);
                                    } else {
                                        fs.setThisLayerSelect(2);
                                    }
                                }
                            }
                        }
                        // 子层添加
                        if (fs.getSubLayerAdd() != null) {
                            if (fs.getSubLayerAdd() == 0) {
                                if (t.getSubLayerAdd() != null) {
                                    if (t.getSubLayerAdd() == 1) {
                                        fs.setSubLayerAdd(1);
                                    } else {
                                        fs.setSubLayerAdd(0);
                                    }
                                }
                            } else if (fs.getSubLayerAdd() == 3) {
                                if (t.getSubLayerAdd() != null) {
                                    if (t.getSubLayerAdd() == 1) {
                                        fs.setSubLayerAdd(1);
                                    } else {
                                        fs.setSubLayerAdd(3);
                                    }
                                }
                            } else {
                                if (t.getSubLayerAdd() != null) {
                                    if (t.getSubLayerAdd() == 1) {
                                        if (isAdministrator) {
                                            fs.setSubLayerAdd(1);
                                        } else {
                                            fs.setSubLayerAdd(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getSubLayerAdd() != null) {
                                if (t.getSubLayerAdd() == 1) {
                                    if (isAdministrator) {
                                        fs.setSubLayerAdd(1);
                                    } else {
                                        fs.setSubLayerAdd(2);
                                    }
                                }
                            }
                        }

                        if (fs.getSubLayerEdit() != null) {
                            if (fs.getSubLayerEdit() == 0) {
                                if (t.getSubLayerEdit() != null) {
                                    if (t.getSubLayerEdit() == 1) {
                                        fs.setSubLayerEdit(1);
                                    } else {
                                        fs.setSubLayerEdit(0);
                                    }
                                }
                            } else if (fs.getSubLayerEdit() == 3) {
                                if (t.getSubLayerEdit() != null) {
                                    if (t.getSubLayerEdit() == 1) {
                                        fs.setSubLayerEdit(1);
                                    } else {
                                        fs.setSubLayerEdit(3);
                                    }
                                }
                            } else {
                                if (t.getSubLayerEdit() != null) {
                                    if (t.getSubLayerEdit() == 1) {
                                        if (isAdministrator) {
                                            fs.setSubLayerEdit(1);
                                        } else {
                                            fs.setSubLayerEdit(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getSubLayerEdit() != null) {
                                if (t.getSubLayerEdit() == 1) {
                                    if (isAdministrator) {
                                        fs.setSubLayerEdit(1);
                                    } else {
                                        fs.setSubLayerEdit(2);
                                    }
                                }
                            }
                        }

                        if (fs.getSubLayerDelete() != null) {
                            if (fs.getSubLayerDelete() == 0) {
                                if (t.getSubLayerDelete() != null) {
                                    if (t.getSubLayerDelete() == 1) {
                                        fs.setSubLayerDelete(1);
                                    } else {
                                        fs.setSubLayerDelete(0);
                                    }
                                }
                            } else if (fs.getSubLayerDelete() == 3) {
                                if (t.getSubLayerDelete() != null) {
                                    if (t.getSubLayerDelete() == 1) {
                                        fs.setSubLayerDelete(1);
                                    } else {
                                        fs.setSubLayerDelete(3);
                                    }
                                }
                            } else {
                                if (t.getSubLayerDelete() != null) {
                                    if (t.getSubLayerDelete() == 1) {
                                        if (isAdministrator) {
                                            fs.setSubLayerDelete(1);
                                        } else {
                                            fs.setSubLayerDelete(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getSubLayerDelete() != null) {
                                if (t.getSubLayerDelete() == 1) {
                                    if (isAdministrator) {
                                        fs.setSubLayerDelete(1);
                                    } else {
                                        fs.setSubLayerDelete(2);
                                    }
                                }
                            }
                        }

                        if (fs.getSubLayerSelect() != null) {
                            if (fs.getSubLayerSelect() == 0) {
                                if (t.getSubLayerSelect() != null) {
                                    if (t.getSubLayerSelect() == 1) {
                                        fs.setSubLayerSelect(1);
                                    } else {
                                        fs.setSubLayerSelect(0);
                                    }
                                }
                            } else if (fs.getSubLayerSelect() == 3) {
                                if (t.getSubLayerSelect() != null) {
                                    if (t.getSubLayerSelect() == 1) {
                                        fs.setSubLayerSelect(1);
                                    } else {
                                        fs.setSubLayerSelect(3);
                                    }
                                }
                            } else {
                                if (t.getSubLayerSelect() != null) {
                                    if (t.getSubLayerSelect() == 1) {
                                        if (isAdministrator) {
                                            fs.setSubLayerSelect(1);
                                        } else {
                                            fs.setSubLayerSelect(2);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (t.getSubLayerSelect() != null) {
                                if (t.getSubLayerSelect() == 1) {
                                    if (isAdministrator) {
                                        fs.setSubLayerSelect(1);
                                    } else {
                                        fs.setSubLayerSelect(2);
                                    }
                                }
                            }
                        }


                    }
                });
            });
            selectorModels = finalSelectorModels1;
        }
//        List<String> collect = selectorModels.stream().map(SumTree::getParentId).collect(Collectors.toList());
//        List<OrganizeAdministratorSelectorModel> noParentId = selectorModels.stream().filter(t -> !collect.contains(t.getId()) && !"-1".equals(t.getParentId())).collect(Collectors.toList());

        // 判断断层有没有上下级关系
        List<OrganizeAdministratorSelectorModel> finalSelectorModels2 = new ArrayList<>(selectorModels);


        selectorModels.forEach(t -> {
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                List<String> list1 = new ArrayList<>();
                String[] split = t.getOrganizeIdTree().split(",");
                list1 = Arrays.asList(split);
                Collections.reverse(list1);
                for (String orgId : list1) {
                    OrganizeAdministratorSelectorModel organizeEntity1 = finalSelectorModels2.stream().filter(organizeEntity -> organizeEntity.getId().equals(orgId)).findFirst().orElse(null);
                    if (organizeEntity1 != null && !organizeEntity1.getId().equals(t.getId())) {
                        t.setParentId(organizeEntity1.getId());
                        String[] split1 = t.getOrganizeIdTree().split(organizeEntity1.getId());
                        if (split1.length > 1) {
                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        break;
                    }
                }
            }
        });
//        // 移除掉没有的
//        selectorModels.removeAll(noParentId);
//        noParentId.forEach(t -> {
//            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
//                String[] split = t.getOrganizeIdTree().split(",");
//                List<String> list = Arrays.asList(split);
//                Collections.reverse(list);
//                for (int i = 1; i < list.size(); i++) {
//                    String orgId = list.get(i);
//                    List<OrganizeAdministratorSelectorModel> collect1 = finalOrganizeList.stream().filter(tt -> orgId.equals(tt.getId())).collect(Collectors.toList());
//                    if (collect1.size() > 0) {
//                        String[] split1 = StringUtil.isNotEmpty(t.getOrganizeIdTree()) ? t.getOrganizeIdTree().split(orgId) : new String[0];
//                        if (split1.length > 0) {
//                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
//                        }
//                        t.setParentId(orgId);
//                        break;
//                    }
//                }
//            }
//        });
//        // 增加掉没有的
//        selectorModels.addAll(noParentId);
        selectorModels.forEach(t -> t.setIcon(StringUtil.isNotEmpty(t.getCategory()) ? "company".equals(t.getCategory()) ? "icon-ym icon-ym-tree-organization3" : "icon-ym icon-ym-tree-department1" : ""));
        List<SumTree<OrganizeAdministratorSelectorModel>> trees = TreeDotUtils.convertListToTreeDot(selectorModels);
        List<OrganizeAdministratorSelectorVO> listVO = JsonUtil.getJsonToList(trees, OrganizeAdministratorSelectorVO.class);
        listVO.forEach(t -> {
            t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
        });
        ListVO<OrganizeAdministratorSelectorVO> vo = new ListVO<>();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

//	/**
//	 *
//	 * @return
//	 */
//	@Operation(summary = "拉取机构分级管理")
//	@GetMapping("/{organizeId}")
//	public ActionResult info(@PathVariable("organizeId") String organizeId) throws DataException{
//		OrganizeAdministratorEntity entity = organizeAdminIsTratorService.getInfoByOrganizeId(organizeId);
//		if(entity!=null){
//			OrganizeAdminIsTratorInfoVo vo = JsonUtilEx.getJsonToBeanEx(entity, OrganizeAdminIsTratorInfoVo.class);
//			return ActionResult.success(vo);
//		}else{
//			return ActionResult.success(entity);
//		}
//	}
//
//	/**
//	 * 创建
//	 *
//	 * @param organizeAdminIsTratorCrForm
//	 * @return
//	 */
//	@OrganizeAdminIsTrator
//	@Operation(summary = "新建机构分级管理员")
//	@PostMapping
//	public ActionResult create(@RequestBody @Valid OrganizeAdminIsTratorCrForm organizeAdminIsTratorCrForm) {
//		OrganizeAdministratorEntity entity = JsonUtil.getJsonToBean(organizeAdminIsTratorCrForm, OrganizeAdministratorEntity.class);
//		entity.setId(RandomUtil.uuId());
//		organizeAdminIsTratorService.create(entity);
//		return ActionResult.success(MsgCode.SU001.get());
//	}
//
//	/**
//	 * 更新组织
//	 *
//	 * @param organizeId                          主键值
//	 * @param organizeAdminIsTratorCrForm 实体对象
//	 * @return
//	 */
//	@OrganizeAdminIsTrator
//	@Operation(summary = "更新机构分级管理")
//	@PutMapping("/{organizeId}")
//	public ActionResult update(@PathVariable("organizeId") String organizeId, @RequestBody @Valid OrganizeAdminIsTratorCrForm organizeAdminIsTratorCrForm) {
//
//		OrganizeAdministratorEntity entity = JsonUtil.getJsonToBean(organizeAdminIsTratorCrForm, OrganizeAdministratorEntity.class);
//
//		//查看是否存在
//		OrganizeAdministratorEntity getEntity = organizeAdminIsTratorService.getInfoByOrganizeId(organizeId);
//		if(getEntity == null){
//			entity.setId(RandomUtil.uuId());
//			organizeAdminIsTratorService.create(entity);
//			return ActionResult.success(MsgCode.SU004.get());
//		}
//
//		boolean flag = organizeAdminIsTratorService.update(organizeId, entity);
//		if (flag == false) {
//			return ActionResult.fail(MsgCode.FA002.get());
//		}
//		return ActionResult.success(MsgCode.SU004.get());
//	}
//
//	/**
//	 * 删除组织
//	 *
//	 * @param id 主键值
//	 * @return
//	 */
//	@Operation(summary = "删除机构分级管理")
//	@DeleteMapping("/{id}")
//	public ActionResult delete(@PathVariable("id") String id) {
//		OrganizeAdministratorEntity organizeAdminIsTratorEntity = organizeAdminIsTratorService.getInfoByOrganizeId(id);
//		if (organizeAdminIsTratorEntity != null) {
//			organizeAdminIsTratorService.delete(organizeAdminIsTratorEntity);
//			return ActionResult.success(MsgCode.SU003.get());
//		} else {
//			return ActionResult.fail("查无此记录");
//		}
//	}


//	List<OrganizeEntity> allList = organizeService.getList();
//	List<OrganizeEntity> list = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
//	List<OrganizeAdministratorModel> models = JsonUtil.getJsonToList(list, OrganizeAdministratorModel.class);
//	// 当前用户组织权限
//		if (!userProvider.get().getIsAdministrator()) {
//		List<OrganizeAdministratorEntity> organizeAdministratorEntity = organizeAdminIsTratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
//		// 有查看权限的都显示
//		Set<String> orgId = new HashSet<>(16);
//		// 判断自己是哪些组织的管理员
//		organizeAdministratorEntity.stream().forEach(t-> {
//			if (t != null) {
//				if ("1".equals(String.valueOf(t.getThisLayerAdd())) || t.getThisLayerEdit() == 1 || t.getThisLayerDelete() == 1 || t.getThisLayerSelect() == 1) {
//					orgId.add(t.getOrganizeId());
//				}
//				if (t.getSubLayerAdd() == 1 || t.getSubLayerEdit() == 1 || t.getSubLayerDelete() == 1 || t.getSubLayerSelect() == 1) {
//					List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
//					orgId.addAll(underOrganizations);
//				}
//			}
//		});
//		// 取交集
//		models = models.stream().filter(x -> orgId.contains(x.getId())).collect(Collectors.toList());
//	}
//		for (OrganizeAdministratorModel model : models) {
//		if ("department".equals(model.getType())) {
//			model.setIcon("icon-ym icon-ym-tree-department1");
//		} else {
//			model.setIcon("icon-ym icon-ym-tree-organization3");
//		}
//		// 权限分配
//		if (StringUtil.isNotEmpty(userId)) {
//			OrganizeAdministratorEntity organizeAdministratorEntity = organizeAdminIsTratorService.getOne(userId, model.getId());
//			if (organizeAdministratorEntity != null) {
//				model.setThisLayerAdd(organizeAdministratorEntity.getThisLayerAdd());
//				model.setThisLayerEdit(organizeAdministratorEntity.getThisLayerEdit());
//				model.setThisLayerDelete(organizeAdministratorEntity.getThisLayerDelete());
//				model.setThisLayerSelect(organizeAdministratorEntity.getThisLayerSelect());
//				model.setSubLayerAdd(organizeAdministratorEntity.getSubLayerAdd());
//				model.setSubLayerEdit(organizeAdministratorEntity.getSubLayerEdit());
//				model.setSubLayerDelete(organizeAdministratorEntity.getSubLayerDelete());
//				model.setSubLayerSelect(organizeAdministratorEntity.getSubLayerSelect());
//			}
//		}
//	}
//	List<SumTree<OrganizeAdministratorModel>> trees = TreeDotUtils.convertListToTreeDot(models);
//	List<OrganizeAdministratorTreeVO> listVO = JsonUtil.getJsonToList(trees, OrganizeAdministratorTreeVO.class);
//	//		//将子节点全部删除
////		Iterator<OrganizeAdministratorTreeVO> iterator = listVO.iterator();
////		while (iterator.hasNext()) {
////			OrganizeAdministratorTreeVO organizeAdministratorTreeVO = iterator.next();
////			if (!"-1".equals(organizeAdministratorTreeVO.getParentId())) {
////				iterator.remove();
////			}
////		}
//	ListVO vo = new ListVO();
//		vo.setList(listVO);
//		return ActionResult.success(vo);
}
