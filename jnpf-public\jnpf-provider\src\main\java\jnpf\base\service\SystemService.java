package jnpf.base.service;

import jnpf.base.UserInfo;
import jnpf.base.service.SuperService;
import com.baomidou.mybatisplus.extension.service.IService;
import jnpf.base.entity.SystemEntity;

import java.util.List;

/**
 * 系统
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月27日 上午9:18
 */
public interface SystemService extends SuperService<SystemEntity> {

    /**
     * 获取系统列表
     *
     * @param keyword
     * @return
     */
    List<SystemEntity> getList(String keyword, Boolean filterEnableMark);

    /**
     * 获取系统列表
     *
     * @param keyword
     * @return
     */
    List<SystemEntity> getList(String keyword, Boolean filterEnableMark, boolean verifyAuth);

    /**
     * 获取当前用户所有系统权限
     *
     * @param userInfo
     * @return
     */
    List<String> getCurrentUserSystem(UserInfo userInfo);

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    SystemEntity getInfo(String id);

    /**
     * 判断系统名称是否重复
     *
     * @param id
     * @param fullName
     * @return
     */
    Boolean isExistFullName(String id, String fullName);

    /**
     * 判断系统编码是否重复
     *
     * @param id
     * @param enCode
     * @return
     */
    Boolean isExistEnCode(String id, String enCode);

    /**
     * 新建
     *
     * @param entity
     * @return
     */
    Boolean create(SystemEntity entity);

    /**
     * 新建
     *
     * @param entity
     * @return
     */
    Boolean update(String id, SystemEntity entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delete(String id);

    /**
     *
     * 通过id获取系统列表
     *
     * @param list
     * @return
     */
    List<SystemEntity> getListByIds(List<String> list);

    /**
     * 获取主系统
     *
     * @return
     */
    SystemEntity getMainSystem();

    /**
     * 获取主系统
     *
     * @param systemIds
     * @return
     */
    List<SystemEntity> getMainSys(List<String> systemIds);
}
