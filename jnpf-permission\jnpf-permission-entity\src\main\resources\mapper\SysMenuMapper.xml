<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.permission.mapper.SysMenuMapper">

    <resultMap id="BaseResultMap" type="jnpf.base.entity.RuoYiSysMenuEntity">
            <id property="menuId" column="menu_id" jdbcType="BIGINT"/>
            <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="component" column="component" jdbcType="VARCHAR"/>
            <result property="query" column="query" jdbcType="VARCHAR"/>
            <result property="isFrame" column="is_frame" jdbcType="INTEGER"/>
            <result property="isCache" column="is_cache" jdbcType="INTEGER"/>
            <result property="menuType" column="menu_type" jdbcType="CHAR"/>
            <result property="visible" column="visible" jdbcType="CHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="perms" column="perms" jdbcType="VARCHAR"/>
            <result property="icon" column="icon" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        menu_id,menu_name,parent_id,
        order_num,path,component,
        query,is_frame,is_cache,
        menu_type,visible,status,
        perms,icon,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
</mapper>
