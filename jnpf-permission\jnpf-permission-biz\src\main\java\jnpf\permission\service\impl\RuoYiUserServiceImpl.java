package jnpf.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jnpf.base.Pagination;
import jnpf.base.UserInfo;
import jnpf.base.service.SuperServiceImpl;
import jnpf.model.LoginUser;
import jnpf.model.SysUser;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.entity.UserRelationEntity;
import jnpf.permission.mapper.RuoYiUserMapper;
import jnpf.permission.service.RuoYiSysUserService;
import jnpf.util.RedisCache;
import jnpf.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Service
@DS("ruoyi")
@RequiredArgsConstructor
public class RuoYiUserServiceImpl extends SuperServiceImpl<RuoYiUserMapper, SysUser> implements RuoYiSysUserService {

    private final RedisCache redisCache;

    public static final String AQSOC_USER_ENTITY_KEY = "aqsoc_user_entity_key";
    private static final String UNKNOWN_GENDER = "未知";
    private static final int UNKNOWN_SEX = 2;
    private static final int MALE_SEX = 0;
    private static final int FEMALE_SEX = 1;

    private static final long USER_ENTITY_CACHE_TIMEOUT = 2; // 缓存失效时间以天为单位的常量

    /**
     * 从给定的用户ID集合中查询用户列表。
     * 如果用户ID集合不为空，则从缓存中获取用户列表，然后根据用户ID过滤并返回结果。
     * 如果缓存中不存在某些用户ID，则从数据库中查询并将结果添加到列表中。
     *
     * @param userIds 用户ID集合
     * @return 用户列表
     */
    @Override
    public List<UserEntity> selectUserList(List<String> userIds) {
        List<UserEntity> userEntityList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userIds)) {
            Set<Long> userIdSet = userIds.stream().filter(StrUtil::isNotBlank).map(String::trim) // 去除空格
                    .map(Long::valueOf) // 将String转换为Long
                    .collect(Collectors.toSet()); // 收集为Set<Long>
            Set<UserEntity> cacheSet = null;
            // 如果存在缓存，从缓存中获取若依用户信息
            if (redisCache.hasKey(AQSOC_USER_ENTITY_KEY)) {
                cacheSet = redisCache.getCacheSet(AQSOC_USER_ENTITY_KEY);
            }
            // 缓存为空，从数据库查询
            if (CollectionUtil.isEmpty(cacheSet)) {
                userEntityList.addAll(this.getListByIds(userIdSet));
            } else {
                // 通过userIds过滤缓存中存在的用户信息
                userEntityList = cacheSet.stream().filter(userEntity -> userIdSet.contains(userEntity.getId())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(userEntityList)) {
                    // 缓存中不存在的将从数据库中查询
                    // 直接遍历userEntityList来获取存在的ID，并转换为Set
                    Set<Long> existingUserIds = userEntityList.stream().map(UserEntity::getId).map(Long::parseLong).collect(Collectors.toSet());
                    // 使用交集来找到不存在的ID
                    Set<Long> nonExistingUserIds = userIdSet.stream().filter(id -> !existingUserIds.contains(id)).collect(Collectors.toSet());
                    if (CollectionUtil.isNotEmpty(nonExistingUserIds)) {
                        userEntityList.addAll(this.getListByIds(nonExistingUserIds));
                    }
                } else {
                    // 如果缓存中不存在userIds中的用户
                    userEntityList.addAll(this.getListByIds(userIdSet));
                }
            }
            // 将若依用户信息添加到缓存中
            if (CollectionUtil.isNotEmpty(userEntityList)) {
                if (CollectionUtil.isEmpty(cacheSet)) {
                    cacheSet = new HashSet<>();
                }
                cacheSet.addAll(userEntityList);
                redisCache.setCacheSet(AQSOC_USER_ENTITY_KEY, cacheSet);
                // 设置失效时间为2天
                redisCache.expire(AQSOC_USER_ENTITY_KEY, USER_ENTITY_CACHE_TIMEOUT, TimeUnit.DAYS);
            }
        }
        return userEntityList;
    }


    @Override
    public List<UserEntity> getListByIds(Set<Long> userIds) {
        List<UserEntity> userEntityList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userIds)) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SysUser::getUserId, userIds);
            List<SysUser> sysUsers = this.list(queryWrapper);
            for (SysUser sysUser : sysUsers) {
                UserEntity userEntity = createUserEntity(sysUser);
                userEntityList.add(userEntity);
            }
        }
        return userEntityList;
    }

    /**
     * 组装JNPF用户信息
     *
     * @param sysUser
     * @return
     */
    private UserEntity createUserEntity(SysUser sysUser) {
        UserEntity userEntity = new UserEntity();
        userEntity.setAccount(sysUser.getUserName());
        userEntity.setRealName(sysUser.getNickName());
        userEntity.setQuickQuery(sysUser.getUserName());
        userEntity.setNickName(sysUser.getNickName());
        userEntity.setHeadIcon(sysUser.getAvatar());
        int sexInt = assignSexInt(sysUser.getSex());
        userEntity.setGender(sexInt);
        userEntity.setBirthday(sysUser.getCreateTime());
        userEntity.setMobilePhone(sysUser.getPhonenumber());
        userEntity.setEmail(sysUser.getEmail());
        userEntity.setEntryDate(sysUser.getCreateTime());
        userEntity.setPrevLogTime(sysUser.getLoginDate());
        userEntity.setLastLogTime(sysUser.getLoginDate());
        userEntity.setLastLogIp(sysUser.getLoginIp());
        userEntity.setIsAdministrator(sysUser.getUserId() == 1L ? 1 : 0);
        userEntity.setRoleId(String.valueOf(sysUser.getRoleId()));
        userEntity.setSystemId("");
        userEntity.setAppSystemId("");
        userEntity.setSortCode(0L);
        userEntity.setEnabledMark(isEnabled(sysUser.getDelFlag()));
        userEntity.setId(String.valueOf(sysUser.getUserId()));
        return userEntity;
    }

    /**
     * 性别字符串转换为Int
     *
     * @param sex
     * @return
     */
    private int assignSexInt(String sex) {
        switch (sex) {
            case "男":
                return MALE_SEX;
            case "女":
                return FEMALE_SEX;
            default:
                return UNKNOWN_SEX;
        }
    }

    private int isEnabled(String delFlag) {
        return Objects.equals(delFlag, "0") ? 1 : 0;
    }


    @Override
    public UserEntity getUserByAccount(String account) {
        UserEntity userEntity = null;
        if (StrUtil.isNotBlank(account)) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysUser::getUserName, account);
            queryWrapper.lambda().ne(SysUser::getDelFlag, "2");
            SysUser sysUser = this.getOne(queryWrapper);
            if (Objects.nonNull(sysUser)) {
                userEntity = createUserEntity(sysUser);
            }
        }
        return userEntity;
    }

    @Override
    public UserEntity getInfo(String id) {
        UserEntity userEntity = null;
        if (StrUtil.isNotBlank(id)) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysUser::getUserId, Long.valueOf(id));
            SysUser sysUser = this.getOne(queryWrapper);
            if (Objects.nonNull(sysUser)) {
                userEntity = createUserEntity(sysUser);
            }
        }
        return userEntity;
    }

    @Override
    public List<UserEntity> getUserName(List<String> id, Pagination pagination) {
        List<UserEntity> list = new ArrayList<>();
        id.removeAll(Collections.singleton(null));
        if (id.size() > 0) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            if (!StringUtil.isEmpty(pagination.getKeyword())) {
                queryWrapper.lambda().and(t -> t.like(SysUser::getNickName, pagination.getKeyword()).or().like(SysUser::getUserName, pagination.getKeyword()));
            }
            List<Long> idLongList = id.stream().map(Long::parseLong).collect(Collectors.toList());
            queryWrapper.lambda().in(SysUser::getUserId, idLongList);
            queryWrapper.lambda().ne(SysUser::getDelFlag, "2");
            queryWrapper.lambda().select(SysUser::getUserId, SysUser::getNickName, SysUser::getUserName, SysUser::getSex, SysUser::getAvatar, SysUser::getPhonenumber);
            Page<SysUser> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
            IPage<SysUser> iPage = this.page(page, queryWrapper);
            List<SysUser> records = iPage.getRecords();
            // 转换为JNPF用户实体
            for (SysUser sysUser : records) {
                UserEntity userEntity = createUserEntity(sysUser);
                list.add(userEntity);
            }
            return pagination.setData(list, iPage.getTotal());
        }
        return pagination.setData(list, list.size());
    }


    @Override
    public List<UserRelationEntity> getUserByDept(List<String> deptIdList) {
        List<UserRelationEntity> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(deptIdList)) {
            List<Long> deptIdLongList = deptIdList.stream().map(Long::valueOf).collect(Collectors.toList());
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(SysUser::getDeptId, deptIdLongList);
            queryWrapper.lambda().ne(SysUser::getDelFlag, "2");
            List<SysUser> sysUserList = this.list(queryWrapper);
            if (CollUtil.isNotEmpty(sysUserList)) {
                for (SysUser sysUser : sysUserList) {
                    list.add(createUserRelationEntity(sysUser));
                }
            }
        }
        return list;
    }

    /**
     * 通过sysUser组装UserRelationEntity
     *
     * @param sysUser
     * @return
     */
    private UserRelationEntity createUserRelationEntity(SysUser sysUser) {
        UserRelationEntity userRelationEntity = new UserRelationEntity();
        userRelationEntity.setUserId(String.valueOf(sysUser.getUserId()));
        if(sysUser != null && sysUser.getDeptId() != null){
            userRelationEntity.setDeptId(String.valueOf(sysUser.getDeptId()));
        }
        // Position(岗位，目前安全运营平台没用到)，Organize(组织/部门)，Role(角色)
        userRelationEntity.setObjectType("Organize");
        userRelationEntity.setObjectId(String.valueOf(sysUser.getDeptId()));
        userRelationEntity.setSortCode(0L);
        userRelationEntity.setCreatorTime(sysUser.getCreateTime());
        userRelationEntity.setCreatorUserId("");
        userRelationEntity.setTenantId("");
        userRelationEntity.setId("");
        return userRelationEntity;
    }

    @Override
    public List<UserRelationEntity> getUserByRole(List<String> roleIds) {
        List<UserRelationEntity> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(roleIds)) {
            List<Long> roleIdLongList = roleIds.stream().map(Long::valueOf).collect(Collectors.toList());
            List<SysUser> userList = this.getBaseMapper().listSysUserByRole(roleIdLongList);
            if (CollUtil.isNotEmpty(userList)) {
                for (SysUser sysUser : userList) {
                    list.add(createUserRelationEntity(sysUser));
                }
            }
        }
        return list;
    }


    @Override
    public UserInfo getUserInfoByAccount(String account) {
        UserInfo userInfo = null;
        if (StrUtil.isNotBlank(account)) {
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysUser::getUserName, account);
            queryWrapper.lambda().ne(SysUser::getDelFlag, "2");
            SysUser sysUser = this.getOne(queryWrapper);
            if (Objects.nonNull(sysUser)) {
                userInfo = createUserInfo(sysUser);
            }
        }
        return userInfo;
    }

    public static UserInfo createUserInfo(SysUser sysUser) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId("");
        userInfo.setUserId(String.valueOf(sysUser.getUserId()));
        userInfo.setUserAccount(sysUser.getUserName());
        userInfo.setUserName(sysUser.getNickName());
        userInfo.setUserIcon(sysUser.getAvatar());
        userInfo.setUserGender(sysUser.getSex());
        userInfo.setTheme("");
        userInfo.setOrganizeId("");
        userInfo.setDepartmentId(String.valueOf(sysUser.getDeptId()));
        userInfo.setRoleIds(Arrays.asList(String.valueOf(sysUser.getRoleId())));
        userInfo.setLoginTime(DateUtil.format(sysUser.getLoginDate(), "yyyy-MM-dd HH:mm:ss"));
        userInfo.setLoginIpAddress(sysUser.getLoginIp());
        userInfo.setPrevLoginTime(sysUser.getLoginDate());
        userInfo.setIsAdministrator(sysUser.getUserId() == 1L);
        userInfo.setTenantId("");
        userInfo.setTenantDbConnectionString("");
        userInfo.setAssignDataSource(false);
        userInfo.setPortalId("");
        userInfo.setSystemId("");
        userInfo.setAppSystemId("");
        userInfo.setGrantType("password");
        return userInfo;
    }

}
