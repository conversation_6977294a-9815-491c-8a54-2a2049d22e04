package jnpf.service;


import jnpf.base.service.SuperService;
import com.baomidou.mybatisplus.extension.service.IService;
import jnpf.entity.DocumentEntity;
import jnpf.entity.DocumentShareEntity;
import jnpf.model.document.PageDocument;

import java.util.List;

/**
 * 知识文档
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
public interface DocumentService extends SuperService<DocumentEntity> {

    /**
     * 列表（全部文档）
     *
     * @return
     */
    List<DocumentEntity> getFolderList();

    /**
     * 列表（全部文档）
     *
     * @param parentId 文档父级
     * @return
     */
    List<DocumentEntity> getAllList(String parentId);

    /**
     * 列表（全部文档）
     *
     * @param page
     * @return
     */
    List<DocumentEntity> getAllList(PageDocument page);

    /**
     * 列表（回收站）
     */
    List<DocumentEntity> getTrashList();

    /**
     * 列表（我的共享）
     */
    List<DocumentEntity> getShareOutList();

    /**
     * 列表（共享给我）
     */
    List<DocumentEntity> getShareTomeList();

    /**
     * 列表（共享人员）
     *
     * @param documentId 文档主键
     * @return
     */
    List<DocumentShareEntity> getShareUserList(String documentId);

    /**
     * 信息
     *
     * @param id 主键值
     * @return
     */
    DocumentEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 实体对象
     */
    void delete(DocumentEntity entity);

    /**
     * 创建
     *
     * @param entity 实体对象
     */
    void create(DocumentEntity entity);

    /**
     * 更新
     *
     * @param id     主键值
     * @param entity 实体对象
     */
    boolean update(String id, DocumentEntity entity);

    /**
     * 共享文件（创建）
     *
     * @param documentId  文档主键
     * @param shareUserId 共享用户
     */
    boolean shareCreate(String documentId, String[] shareUserId);

    /**
     * 共享文件（取消）
     *
     * @param documentId 文档主键
     */
    boolean shareCancel(String documentId);

    /**
     * 回收站（删除）
     *
     * @param folderId 文件夹主键值
     */
    void trashDelete(String folderId);

    /**
     * 回收站（还原）
     *
     * @param id 主键值
     */
    boolean trashRecovery(String id);

    /**
     * 文件/夹移动到
     *
     * @param id   主键值
     * @param toId 将要移动到Id
     */
    boolean moveTo(String id, String toId);

    /**
     * 验证文件名是否重复
     *
     * @param id   主键值
     * @param fullName 文件夹名称
     */
    boolean isExistByFullName(String fullName, String id, String parentId);
}
