package jnpf.permission.entity;

import jnpf.base.entity.SuperBaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户关系
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("base_userrelation")
public class UserRelationEntity extends SuperBaseEntity.SuperCBaseEntity<String> {

    /**
     * 用户主键
     */
    @TableField("F_USERID")
    private String userId;

    /**
     * 部门
     */
    @TableField(exist = false)
    private String deptId;

    /**
     * 对象类型
     */
    @TableField("F_OBJECTTYPE")
    private String objectType;

    /**
     * 对象主键
     */
    @TableField("F_OBJECTID")
    private String objectId;

    /**
     * 排序码
     */
    @TableField("F_SORTCODE")
    private Long sortCode;

}
