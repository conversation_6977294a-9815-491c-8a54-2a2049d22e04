package jnpf.permission.model.organize;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date ：2022/6/28 9:35
 */
@Data
public class OrganizeDepartSelectorByAuthListVO extends OrganizeDepartSelectorListVO implements Serializable {

    @Schema(description = "是否可选")
    private Boolean disabled = false;

}
