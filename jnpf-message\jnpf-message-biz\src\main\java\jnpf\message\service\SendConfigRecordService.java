
package jnpf.message.service;

import jnpf.base.service.SuperService;
import com.baomidou.mybatisplus.extension.service.IService;


import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jnpf.message.entity.SendConfigRecordEntity;
import jnpf.message.model.sendconfigrecord.*;

/**
 * 发送配置使用记录
 * 版本： V3.2.0
 * 版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * 作者： JNPF开发平台组
 * 日期： 2022-09-21
 */
public interface SendConfigRecordService extends SuperService<SendConfigRecordEntity> {


    List<SendConfigRecordEntity> getList(SendConfigRecordPagination sendConfigRecordPagination);

    List<SendConfigRecordEntity> getTypeList(SendConfigRecordPagination sendConfigRecordPagination, String dataType);


    SendConfigRecordEntity getInfo(String id);

    SendConfigRecordEntity getRecord(String sendConfigId, String usedId);

    void delete(SendConfigRecordEntity entity);

    void create(SendConfigRecordEntity entity);

    boolean update(String id, SendConfigRecordEntity entity);

//  子表方法

    //列表子表数据方法

    //验证表单
    boolean checkForm(SendConfigRecordForm form, int i);
}
