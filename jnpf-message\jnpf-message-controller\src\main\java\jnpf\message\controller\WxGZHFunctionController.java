package jnpf.message.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.entity.SysConfigEntity;
import jnpf.base.service.SysconfigService;
import jnpf.message.entity.AccountConfigEntity;
import jnpf.message.entity.WechatUserEntity;
import jnpf.message.service.AccountConfigService;
import jnpf.message.service.WechatUserService;
import jnpf.message.util.weixingzh.WXGZHWebChatUtil;
import jnpf.model.BaseSystemInfo;
import jnpf.permission.entity.SocialsUserEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.permission.service.SocialsUserService;
import jnpf.permission.service.UserService;
import jnpf.util.DateUtil;
import jnpf.util.JsonUtil;
import jnpf.util.RandomUtil;
import jnpf.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发送消息模型
 *
 * @版本： V2.0
 * @版权： 河北交投智能交通技术有限责任公司
 * @作者： 数字智能项目组
 * @日期： 2021/4/21 10:28
 */
@Tag(name = "微信公众号事件接收", description = "WechatOpen")
@Controller
@RequestMapping("/api/message/WechatOpen")
@Slf4j
public class WxGZHFunctionController {
    private static final String TOKEN = "112233aavvbb";

    @Autowired
    private UserService userService;
    @Autowired
    private SysconfigService sysconfigService;
    @Autowired
    private AccountConfigService accountConfigService;
    @Autowired
    private SocialsUserService socialsUserService;
    @Autowired
    private WechatUserService wechatUserService;

    /**
     * 服务器基本配置链接微信公众号验证
     *
     * @param request 请求对象
     * @param response 响应对象
     * @return
     */
    @Operation(summary = "服务器基本配置链接微信公众号验证")
    @ResponseBody
    @SaCheckPermission("msgCenter.sendConfig")
    @GetMapping("/token")
    public String token(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String signature = request.getParameter("signature");
        String echostr = request.getParameter("echostr");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");

        String sortStr = WXGZHWebChatUtil.sort(TOKEN,timestamp,nonce);
        String MySinStr = WXGZHWebChatUtil.shal(sortStr);
        if(StringUtil.isNotBlank(signature) && MySinStr.equals(signature)){
            return echostr;
        }else {
            log.info("微信公众号链接失败");
            return echostr;
        }
    }


    /**
     * 微信公众号事件请求
     *
     * @param request 请求对象
     * @param response 响应对象
     * @return
     * @throws Exception
     */
    @Operation(summary = "微信公众号事件请求")
    @SaCheckPermission("msgCenter.sendConfig")
    @ResponseBody
    @PostMapping("/token")
    /**
     * 微信公众号事件请求
     */
    public String tokenPost(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("微信公众号请求事件");
        // 获取系统配置

        String signature = request.getParameter("signature");
        String echostr = request.getParameter("echostr");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");

        String sortStr = WXGZHWebChatUtil.sort(TOKEN,timestamp,nonce);
        String MySinStr = WXGZHWebChatUtil.shal(sortStr);
        //验签
        if(StringUtil.isNotBlank(signature) && MySinStr.equals(signature)){
            //事件信息
            Map<String ,String> map = WXGZHWebChatUtil.parseXml(request);
            //事件信息
            String Event = map.get("Event");
            String openid = map.get("FromUserName");
            //公众号原始id
            String gzhId = map.get("ToUserName");

            //获取系统配置
            AccountConfigEntity accountConfigEntity = accountConfigService.getInfoByType(gzhId,"7");
            if(ObjectUtil.isEmpty(accountConfigEntity)){
                log.info("未找到与公众号原始id相对应的配置");
                return "";
            }
            String appId = accountConfigEntity.getAppId();
            String appsecret = accountConfigEntity.getAppSecret();
            String token = WXGZHWebChatUtil.getAccessToken(appId,appsecret);
            if("subscribe".equals(Event)){
                //用户关注事件
                if(StringUtil.isNotBlank(token)){
                    JSONObject rstObj = WXGZHWebChatUtil.getUsetInfo(token,openid);
                    if(rstObj.containsKey("unionid")){
                        String unionid = rstObj.getString("unionid");
                        SocialsUserEntity socialsUserEntity = socialsUserService.getInfoBySocialId(unionid,"wechat_open");
                        if(socialsUserEntity==null){
                            log.info("微信公众号未绑定系统账号，请登录小程序绑定");
                            return "";
                        }else{
                            WechatUserEntity wechatUserEntity = wechatUserService.getInfoByGzhId(socialsUserEntity.getUserId(),gzhId);
                            if(wechatUserEntity==null){
                                WechatUserEntity entity = new WechatUserEntity();
                                entity.setId(RandomUtil.uuId());
                                entity.setUserId(socialsUserEntity.getUserId());
                                entity.setGzhId(gzhId);
                                entity.setCloseMark(1);
                                entity.setCreatorTime(DateUtil.getNowDate());
                                entity.setOpenId(openid);
                                wechatUserService.create(entity);
                                return "";
                            }else {
                                if(wechatUserEntity.getCloseMark()==0){
                                    wechatUserEntity.setCloseMark(1);
                                }
                                wechatUserEntity.setOpenId(openid);
                                wechatUserEntity.setLastModifyTime(DateUtil.getNowDate());
                                wechatUserService.update(wechatUserEntity.getId(),wechatUserEntity);
                            }
                            return "";
                        }
                    }else{
                        log.info("微信公众号未绑定系统账号，请登录小程序绑定");
                        return "";
                    }
                }else{
                    log.error("微信公众号token错误，请查看配置");
                    return "";
                }
            }else if("unsubscribe".equals(Event)){
                //用户取消关注事件
                if(StringUtil.isNotBlank(token)){
                    JSONObject rstObj = WXGZHWebChatUtil.getUsetInfo(token,openid);
                    if(rstObj.containsKey("unionid")){
                        String unionid = rstObj.getString("unionid");
                        SocialsUserEntity socialsUserEntity = socialsUserService.getInfoBySocialId(unionid,"wechat_open");
                        if(socialsUserEntity==null){
                            log.info("微信公众号未绑定系统账号，请登录小程序绑定");
                        }else{
                            WechatUserEntity wechatUserEntity = wechatUserService.getInfoByGzhId(socialsUserEntity.getUserId(),gzhId);
                            if(wechatUserEntity==null){
                                WechatUserEntity entity = new WechatUserEntity();
                                entity.setId(RandomUtil.uuId());
                                entity.setUserId(socialsUserEntity.getUserId());
                                entity.setGzhId(gzhId);
                                entity.setCloseMark(0);
                                entity.setCreatorTime(DateUtil.getNowDate());
                                entity.setOpenId(openid);
                                wechatUserService.create(entity);
                                return "";
                            }else {
                                if(wechatUserEntity.getCloseMark()==1){
                                    wechatUserEntity.setCloseMark(0);
                                }
                                wechatUserEntity.setOpenId(openid);
                                wechatUserEntity.setLastModifyTime(DateUtil.getNowDate());
                                wechatUserService.update(wechatUserEntity.getId(),wechatUserEntity);
                                return "";
                            }
                        }
                    }else{
                        log.info("微信公众号未绑定系统账号，请登录小程序绑定");
                        return "";
                    }
                }else{
                    log.error("微信公众号token错误，请查看配置");
                    return "";
                }
                return "";
            }else {
                return "";
            }
        }else {
            log.info("微信公众号事件请求失败");
            return echostr;
        }
    }

    /**
     * 获取系统配置
     */
    private Map<String, String> getSystemConfig() {
        // 获取系统配置
        List<SysConfigEntity> configList = sysconfigService.getList("SysConfig");
        Map<String, String> objModel = new HashMap<>(16);
        for (SysConfigEntity entity : configList) {
            objModel.put(entity.getFkey(), entity.getValue());
        }
        return objModel;
    }

}
