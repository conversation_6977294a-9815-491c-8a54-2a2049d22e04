package jnpf.entity;

import jnpf.base.entity.SuperBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 流程表单关联表
 *
 * <AUTHOR>
 * @version V3.4.2
 * @copyright 引迈信息技术有限公司
 * @date 2022/10/26 15:58:02
 */
@Data
@TableName("flow_engineform_relation")
public class FlowFormRelationEntity extends SuperBaseEntity.SuperTBaseEntity<String> {

    /**
     * 流程版本id
     */
    @TableField("F_FLOWID")
    private String flowId;
    /**
     * 表单id
     */
    @TableField("F_FORMID")
    private String formId;

}
