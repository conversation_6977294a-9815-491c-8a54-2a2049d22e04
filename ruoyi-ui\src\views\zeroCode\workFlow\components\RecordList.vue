<template>
  <div class="main">
    <el-timeline>
      <el-timeline-item v-for="(item, index) in list" :key="item.id" :timestamp="item.nodeName + '  ' + formDateTime(item.handleTime, '{y}-{m}-{d} {h}:{i}:{s}')"  placement="top">
        <el-card>
          <el-descriptions class="margin-top" :column="3" size="small" border>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                操作人员
              </template>
              {{ item.userName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-date"></i>
                接收时间
              </template>
              {{ formDateTime(item.creatorTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-setting"></i>
                操作时间
              </template>
              {{ formDateTime(item.handleTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-tickets"></i>
                执行动作
              </template>
                <div class="item">
                  <span
                    :style="{ background: colorList[item.handleStatus || 0] }"
                  ></span>
                  {{ statusList[item.handleStatus || 0] }}
                  <div
                    v-if="
                item.handleStatus == 5 ||
                item.handleStatus == 6 ||
                item.handleStatus == 7 ||
                item.handleStatus == 10
              "
                  >
                    ：{{ item.operatorId }}
                  </div>
                </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-news"></i>
                签名
              </template>
              <el-image
                v-if="item.signImg"
                style="width: 80px"
                :src="item.signImg"
                :preview-src-list="[item.signImg]"
              >
              </el-image>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-document"></i>
                附件
              </template>
              <div
                v-for="(file, index) in JSON.parse(item.fileList)"
                :key="index"
                @click="handlePreview(file)"
              >
                <el-link type="primary" :underline="false">{{ file.name }}</el-link>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-chat-line-square"></i>
                审批意见
              </template>
              {{ item.handleOpinion }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-timeline-item>
    </el-timeline>
<!--    <div class="JNPF-common-layout">
      <JNPF-table
        :data="list"
        :hasNO="false"
        class="recordListTable"
        height="100%"
      >
        <el-table-column
          prop="nodeName"
          label="节点名称"
          show-overflow-tooltip
          width="200"
          v-if="opType == 4"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="handelNodeDetail(scope.row)"
            >
              {{ scope.row.nodeName }}</el-link
            >
          </template>
        </el-table-column>
        <el-table-column prop="nodeName" label="节点名称" width="200" v-else />
        <el-table-column prop="userName" label="操作人员" width="150" />
        <el-table-column
          prop="creatorTime"
          label="接收时间"
          width="150"
          :formatter="jnpf.tableDateFormat"
        />
        <el-table-column
          prop="handleTime"
          label="操作时间"
          width="150"
          :formatter="jnpf.tableDateFormat"
        />
        <el-table-column
          prop="handleStatus"
          label="执行动作"
          width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div class="item">
            <span
              :style="{ background: colorList[scope.row.handleStatus || 0] }"
            ></span>
              {{ statusList[scope.row.handleStatus || 0] }}
              <div
                v-if="
                scope.row.handleStatus == 5 ||
                scope.row.handleStatus == 6 ||
                scope.row.handleStatus == 7 ||
                scope.row.handleStatus == 10
              "
              >
                ：{{ scope.row.operatorId }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="signImg" label="签名" width="120" >
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.signImg"
              style="width: 80px"
              :src="scope.row.signImg"
              :preview-src-list="[scope.row.signImg]"
            >
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="fileList" label="附件" width="200" >
          <template slot-scope="scope">
            <div
              v-for="(file, index) in JSON.parse(scope.row.fileList)"
              :key="index"
              @click="handlePreview(file)"
            >
              <el-link type="primary" :underline="false">{{ file.name }}</el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="handleOpinion"
          label="备注"
          min-width="200"
          show-overflow-tooltip
        />
      </JNPF-table>
      <Preview
        :visible.sync="previewVisible"
        :file="activeFile"
        :showDownload="true"
      />
      <FormBox v-if="formVisible" ref="FormBox" @close="formVisible = false" />
    </div>-->
  </div>
</template>
<script>
import Preview from "@/components/Generator/components/Upload/Preview";
import FormBox from "../components/FormBox";
import { parseTime } from '../../../../utils/ruoyi'
import { getDownloadUrl, getPackDownloadUrl } from "@/api/lowCode/common";

export default {
  components: { Preview, FormBox },
  props: {
    list: { type: Array, default: [] },
    endTime: { type: Number, default: 0 },
    flowId: { type: String, default: "" },
    opType: { type: Number, default: 0 },
  },
  name: "recordList",
  data() {
    return {
      colorList: [
        "rgba(242,68,68,0.39)",
        "rgba(35,162,5,0.39)",
        "rgba(0,0,255,0.39)",
        "rgba(21,21,157,0.39)",
        "rgba(186,33,33,0.39)",
        "rgba(25,185,185,0.39)",
        "rgba(50,191,61,0.39)",
        "rgba(49,151,214,0.39)",
        "rgba(185,123,6,0.39)",
        "rgba(45,94,186,0.39)",
        "rgba(50,191,61,0.39)",
        "rgba(255,0,0,0.39)",
        "rgba(0,128,0,0.39)",
      ],
      statusList: [
        "退回",
        "同意",
        "发起",
        "撤回",
        "终止",
        "指派",
        "后加签",
        "转办",
        "变更",
        "复活",
        "前加签",
        "挂起",
        "恢复",
      ],
      previewVisible: false,
      formVisible: false,
      activeFile: {},
    };
  },
  methods: {
    formDateTime(item, par) {
      return parseTime(new Date(item), par)
    },
    handlePreview(file) {
      /*this.activeFile = file;
      this.previewVisible = true;*/
      // 点击下载文件
      if (!file.fileId) return;
      getDownloadUrl("annex", file.fileId).then((res) => {
        this.$download.export("/proxy" + res.data.url, file.name,null,file,true);
      });
    },
    handelNodeDetail(item) {
      let data = {
        id: item.taskId,
        taskNodeId: item.taskNodeId,
        enCode: item.flowCode,
        flowId: this.flowId,
        formType: item.formType,
        opType: 0,
        status: item.status,
        title: item.nodeName,
      };
      this.formVisible = true;
      this.$nextTick(() => {
        this.$refs.FormBox.init(data);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.recordListTable {
  .el-link {
    font-size: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    width: 100%;
    span {
      width: 7px;
      height: 7px;
      margin-right: 6px;
      margin-bottom: 1px;
      border-radius: 50%;
      flex-shrink: 0;
    }
  }
  .signImg {
    width: 80px;
    cursor: pointer;
  }
}

.main{
  overflow-y: auto;
  height: 100%;
  padding: 16px;
  background-color: #f5f7fa;
  > div{
    background-color: #ffffff;
  }
  > div:not(:first-child){
    margin-top: 1.5vh;
  }

  .base_content{
    .ips{
      display: flex;
      > div{
        background-color: #E7F2FF;
        color: #0778FF;
        border-width: 1px;
        border-style: solid;
        border-color: #0778FF;
        border-radius: 3px;
        height: 36px;
        text-align: center;
        overflow: hidden;
        font-size: 0.5vw;
      }
      > div:not(:first-child){
        margin-left: 1%;
      }
      .ips_item{
        width: 31%;
      }
      .ips_item_overflow{
        width: 7%;
      }
    }
  }

  .type_select{
    display: flex;
    padding-top: 10px;
    padding-bottom: 10px;
    .label{
      align-content: center;
    }
    .type_btn{
      display: flex;
      margin-left: 20px;
      > .type_btn_item:not(:first-child){
        margin-left: 10px;
      }

      .btn_active{
        background: #1890ff;
        border-color: #1890ff;
        color: #FFFFFF;
      }
    }
  }
}

::v-deep .el-timeline-item__tail {
  border-left: 2px solid #1890ff;
}
::v-deep .el-timeline-item__node {
  background-color: #1890ff;
}
</style>
