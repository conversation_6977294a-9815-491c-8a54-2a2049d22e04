package jnpf.permission.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.Pagination;
import jnpf.base.entity.DictionaryDataEntity;
import jnpf.base.service.DictionaryDataService;
import jnpf.base.service.DictionaryTypeService;
import jnpf.base.vo.ListVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.constant.MsgCode;
import jnpf.permission.entity.GroupEntity;
import jnpf.permission.model.usergroup.*;
import jnpf.permission.service.GroupService;
import jnpf.util.JsonUtil;
import jnpf.util.enums.DictionaryDataEnum;
import jnpf.util.treeutil.SumTree;
import jnpf.util.treeutil.newtreeutil.TreeDotUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 分组管理控制器
 *
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date ：2022/3/10 17:57
 */
@RestController
@Tag(name = "分组管理", description = "UserGroupController")
@RequestMapping("/api/permission/Group")
public class GroupController extends SuperController<GroupService, GroupEntity> {

    @Autowired
    private GroupService userGroupService;
    @Autowired
    private DictionaryDataService dictionaryDataService;
    @Autowired
    private DictionaryTypeService dictionaryTypeService;

    /**
     * 获取分组管理列表
     *
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "获取分组管理列表")
    @SaCheckPermission(value = {"permission.group"})
    @GetMapping
    public ActionResult<PageListVO<GroupPaginationVO>> list(Pagination pagination) {
        List<GroupEntity> list = userGroupService.getList(pagination);
        List<GroupPaginationVO> jsonToList = JsonUtil.getJsonToList(list, GroupPaginationVO.class);
        // 通过数据字典获取类型
        List<DictionaryDataEntity> dictionaryDataEntities = dictionaryDataService.getList(dictionaryTypeService.getInfoByEnCode(DictionaryDataEnum.PERMISSION_GROUP.getDictionaryTypeId()).getId());
        for (GroupPaginationVO userGroupPaginationVO : jsonToList) {
            DictionaryDataEntity dictionaryDataEntity = dictionaryDataEntities.stream().filter(t -> t.getId().equals(userGroupPaginationVO.getType())).findFirst().orElse(null);
            userGroupPaginationVO.setType(dictionaryDataEntity != null ? dictionaryDataEntity.getFullName() : userGroupPaginationVO.getId());
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
        return ActionResult.page(jsonToList, paginationVO);
    }

    /**
     * 获取分组管理下拉框
     * @return
     */
    @Operation(summary = "获取分组管理下拉框")
    @GetMapping("/Selector")
    public ActionResult<List<GroupSelectorVO>> selector() {
        List<GroupTreeModel> tree = new ArrayList<>();
        List<GroupEntity> data = userGroupService.list();
        List<DictionaryDataEntity> dataEntityList = dictionaryDataService.getList(dictionaryTypeService.getInfoByEnCode(DictionaryDataEnum.PERMISSION_GROUP.getDictionaryTypeId()).getId());
        // 获取分组管理外层菜单
        for (DictionaryDataEntity dictionaryDataEntity : dataEntityList) {
            GroupTreeModel firstModel = JsonUtil.getJsonToBean(dictionaryDataEntity, GroupTreeModel.class);
            firstModel.setId(dictionaryDataEntity.getId());
            firstModel.setType("0");
            long num = data.stream().filter(t -> t.getType().equals(dictionaryDataEntity.getId())).count();
            firstModel.setNum(num);
            if (num > 0) {
                tree.add(firstModel);
            }
        }
        for (GroupEntity entity : data) {
            GroupTreeModel treeModel = JsonUtil.getJsonToBean(entity, GroupTreeModel.class);
            treeModel.setType("group");
            treeModel.setParentId(entity.getType());
            treeModel.setIcon("icon-ym icon-ym-generator-group1");
            treeModel.setId(entity.getId());
            DictionaryDataEntity dataEntity = dictionaryDataService.getInfo(entity.getType());
            if (dataEntity != null) {
                tree.add(treeModel);
            }
        }
        List<SumTree<GroupTreeModel>> sumTrees = TreeDotUtils.convertListToTreeDot(tree);
        List<GroupSelectorVO> list = JsonUtil.getJsonToList(sumTrees, GroupSelectorVO.class);
        ListVO<GroupSelectorVO> vo = new ListVO<>();
        vo.setList(list);
        return ActionResult.success(list);
    }

    /**
     * 信息
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "信息")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @SaCheckPermission(value = {"permission.group"})
    @GetMapping("/{id}")
    public ActionResult<GroupInfoVO> info(@PathVariable("id") String id) {
        GroupEntity entity = userGroupService.getInfo(id);
        GroupInfoVO vo = JsonUtil.getJsonToBean(entity, GroupInfoVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建
     *
     * @param userGroupCrForm 新建模型
     * @return
     */
    @Operation(summary = "创建")
    @Parameters({
            @Parameter(name = "userGroupCrForm", description = "新建模型", required = true)
    })
    @SaCheckPermission(value = {"permission.group"})
    @PostMapping
    public ActionResult create(@RequestBody @Valid GroupCrForm userGroupCrForm) {
        GroupEntity entity = JsonUtil.getJsonToBean(userGroupCrForm, GroupEntity.class);
        // 判断名称是否重复
        if (userGroupService.isExistByFullName(entity.getFullName(), entity.getId())) {
            return ActionResult.fail(MsgCode.EXIST001.get());
        }
        // 判断编码是否重复
        if (userGroupService.isExistByEnCode(entity.getEnCode(), entity.getId())) {
            return ActionResult.fail(MsgCode.EXIST002.get());
        }
        userGroupService.crete(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 更新
     *
     * @param id 主键
     * @param userGroupUpForm 修改模型
     * @return
     */
    @Operation(summary = "更新")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "userGroupUpForm", description = "修改模型", required = true)
    })
    @SaCheckPermission(value = {"permission.group"})
    @PutMapping("/{id}")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid GroupUpForm userGroupUpForm) {
        GroupEntity entity = JsonUtil.getJsonToBean(userGroupUpForm, GroupEntity.class);
        // 判断名称是否重复
        if (userGroupService.isExistByFullName(entity.getFullName(), id)) {
            return ActionResult.fail(MsgCode.EXIST001.get());
        }
        // 判断编码是否重复
        if (userGroupService.isExistByEnCode(entity.getEnCode(), id)) {
            return ActionResult.fail(MsgCode.EXIST002.get());
        }
        Boolean flag = userGroupService.update(id, entity);
        if (!flag) {
            return ActionResult.fail(MsgCode.FA013.get());
        }
        return ActionResult.success(MsgCode.SU004.get());
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "删除")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @SaCheckPermission(value = {"permission.group"})
    @DeleteMapping("/{id}")
    public ActionResult delete(@PathVariable("id") String id) {
        GroupEntity entity = userGroupService.getInfo(id);
        if (entity != null) {
            userGroupService.delete(entity);
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.fail(MsgCode.FA003.get());
    }

}
