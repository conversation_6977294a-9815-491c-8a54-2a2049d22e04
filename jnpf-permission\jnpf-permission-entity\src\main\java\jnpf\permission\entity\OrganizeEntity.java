package jnpf.permission.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 组织机构
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("base_organize")
public class OrganizeEntity extends PermissionEntityBase{

    /**
     * 机构上级
     */
    @TableField("F_PARENTID")
    private String parentId;

    /**
     * 机构分类
     */
    @TableField("F_CATEGORY")
    private String category;

    /**
     * 机构主管
     */
    @TableField("F_MANAGERID")
    private String manager;

    /**
     * 父级组织
     */
    @TableField("F_ORGANIZEIDTREE")
    private String organizeIdTree;

}
