package jnpf.permission.model.organizeadministrator;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 组织管理模型
 *
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date ：2022/5/30 17:42
 */
@Data
public class OrganizeAdminIsTratorModel implements Serializable {
    /**
     * 机构主键
     **/
    @Schema(description = "机构主键")
    private String organizeId;

    /**
     * 机构类型
     **/
    @Schema(description = "机构类型")
    private String organizeType;

    /**
     * 本层添加
     **/
    @Schema(description = "本层添加")
    private Integer thisLayerAdd;

    /**
     * 本层编辑
     **/
    @Schema(description = "本层编辑")
    private Integer thisLayerEdit;

    /**
     * 本层删除
     **/
    @Schema(description = "本层删除")
    private Integer thisLayerDelete;

    /**
     * 子层添加
     **/
    @Schema(description = "子层添加")
    private Integer subLayerAdd;

    /**
     * 子层编辑
     **/
    @Schema(description = "子层编辑")
    private Integer subLayerEdit;

    /**
     * 子层删除
     **/
    @Schema(description = "子层删除")
    private Integer subLayerDelete;

    private Integer thisLayerSelect;

    private Integer subLayerSelect;

    private List<OrganizeAdminIsTratorModel> children;
}
