package jnpf.message.mapper;

import jnpf.base.mapper.SuperMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import jnpf.message.entity.MessageEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 消息实例
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月27日 上午9:18
 */
public interface MessageMapper extends SuperMapper<MessageEntity> {

    List<MessageEntity> getMessageList(@Param("map") Map<String, Object> map);

    int getUnreadNoticeCount(@Param("userId") String userId);

    int getUnreadCount(@Param("userId") String userId,@Param("type") Integer type);

    int getUnreadSystemMessageCount(@Param("userId") String userId);

    int getUnreadMessageCount(@Param("userId") String userId);

    List<MessageEntity> getInfoDefault(@Param("type") int type);
}
