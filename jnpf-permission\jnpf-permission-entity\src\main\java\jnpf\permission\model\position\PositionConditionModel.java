package jnpf.permission.model.position;

import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.base.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date ：2022/6/7 17:05
 */
@Data
public class PositionConditionModel extends Page implements Serializable {

    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private List<String> departIds;

    /**
     * 岗位id
     */
    @Schema(description = "岗位id")
    private List<String> positionIds;
}
