package jnpf.permission.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.annotation.OrganizePermission;
import jnpf.base.ActionResult;
import jnpf.base.Pagination;
import jnpf.base.vo.ListVO;
import jnpf.constant.MsgCode;
import jnpf.message.service.SynThirdDingTalkService;
import jnpf.message.service.SynThirdQyService;
import jnpf.permission.constant.PermissionConst;
import jnpf.permission.entity.OrganizeAdministratorEntity;
import jnpf.permission.entity.OrganizeEntity;
import jnpf.permission.entity.UserEntity;
import jnpf.exception.DataException;
import jnpf.permission.model.organize.*;
import jnpf.permission.service.OrganizeAdministratorService;
import jnpf.permission.service.OrganizeService;
import jnpf.permission.service.UserService;
import jnpf.util.JsonUtil;
import jnpf.util.JsonUtilEx;
import jnpf.util.StringUtil;
import jnpf.util.UserProvider;
import jnpf.util.treeutil.ListToTreeUtil;
import jnpf.util.treeutil.SumTree;
import jnpf.util.treeutil.newtreeutil.TreeDotUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 组织机构
 * 组织架构：公司》部门》岗位》用户
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Tag(name = "组织管理", description = "Organize")
@RestController
@RequestMapping("/api/permission/Organize")
@Slf4j
public class OrganizeController extends SuperController<OrganizeService, OrganizeEntity> {

    @Autowired
    private OrganizeService organizeService;
    @Autowired
    private UserService userService;
    @Autowired
    private SynThirdQyService synThirdQyService;
    @Autowired
    private SynThirdDingTalkService synThirdDingTalkService;
    /**
     * 取出线程池
     */
    @Autowired
    private Executor threadPoolExecutor;
    @Autowired
    private OrganizeAdministratorService organizeAdministratorService;
    @Autowired
    private UserProvider userProvider;

    //---------------------------组织管理--------------------------------------------

    /**
     * 获取组织列表
     *
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "获取组织列表")
    @SaCheckPermission(value = {"permission.organize", "permission.position", "permission.user", "permission.role"}, mode = SaMode.OR)
    @GetMapping
    public ActionResult<ListVO<OrganizeListVO>> getList(Pagination pagination) {
        // 获取所有组织
        List<OrganizeModel> organizeList = JsonUtil
                .getJsonToList(organizeService.getAllCompanyList(pagination.getKeyword()), OrganizeModel.class);
        if (!userProvider.get().getIsAdministrator()) {
            // 通过权限转树
            List<OrganizeAdministratorEntity> listss = organizeAdministratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
            Set<String> orgIds = new HashSet<>(16);
            // 判断自己是哪些组织的管理员
            listss.stream().forEach(t-> {
                if (t != null) {
                    if (t.getThisLayerSelect() != null && t.getThisLayerSelect() == 1) {
                        orgIds.add(t.getOrganizeId());
                    }
                    if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                        List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                        orgIds.addAll(underOrganizations);
                    }
                }
            });
            List<String> list1 = new ArrayList<>(orgIds);
            // 得到所有有权限的组织
            List<OrganizeEntity> organizeName = organizeService.getOrganizeName(list1, pagination.getKeyword());
            organizeList = JsonUtil.getJsonToList(organizeName, OrganizeModel.class);
        }
        List<OrganizeModel> finalOrganizeList = organizeList;
        organizeList.forEach(t -> {
            if (PermissionConst.COMPANY.equals(t.getType())) {
                t.setIcon("icon-ym icon-ym-tree-organization3");
            } else {
                t.setIcon("icon-ym icon-ym-tree-department1");
            }
            // 处理断层
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                List<String> list1 = new ArrayList<>();
                String[] split = t.getOrganizeIdTree().split(",");
                list1 = Arrays.asList(split);
                Collections.reverse(list1);
                for (String orgId : list1) {
                    OrganizeModel organizeEntity1 = finalOrganizeList.stream().filter(organizeEntity -> organizeEntity.getId().equals(orgId)).findFirst().orElse(null);
                    if (organizeEntity1 != null && !organizeEntity1.getId().equals(t.getId())) {
                        t.setParentId(organizeEntity1.getId());
                        String[] split1 = t.getOrganizeIdTree().split(organizeEntity1.getId());
                        if (split1.length > 1) {
                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        break;
                    }
                }
            }
        });
        List<SumTree<OrganizeModel>> trees = TreeDotUtils.convertListToTreeDot(organizeList);
        List<OrganizeListVO> listVO = JsonUtil.getJsonToList(trees, OrganizeListVO.class);
        listVO.forEach(t -> {
            t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
        });
        ListVO<OrganizeListVO> vo = new ListVO<>();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }


    /**
     * 获取组织下拉框列表
     *
     * @param pagination 分页模型
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取组织下拉框列表")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @GetMapping("/Selector/{id}")
    public ActionResult<ListVO<OrganizeSelectorVO>> getSelector(Pagination pagination, @PathVariable("id") String id) {
        List<OrganizeEntity> allList = organizeService.getList();
        if (!"0".equals(id)) {
            allList.remove(organizeService.getInfo(id));
        }
        allList = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<OrganizeEntity> dataAll = allList;
        if (!StringUtil.isEmpty(pagination.getKeyword())) {
            allList = allList.stream().filter(
                    t -> t.getFullName().contains(pagination.getKeyword()) || t.getEnCode().contains(pagination.getKeyword())
            ).collect(Collectors.toList());
        }
        List<OrganizeEntity> list = JsonUtil.getJsonToList(ListToTreeUtil.treeWhere(allList, dataAll), OrganizeEntity.class);
        list = list.stream().filter(t -> "company".equals(t.getCategory())).collect(Collectors.toList());
        List<OrganizeModel> models = JsonUtil.getJsonToList(list, OrganizeModel.class);
        for (OrganizeModel model : models) {
            model.setIcon("icon-ym icon-ym-tree-organization3");
        }

        models.forEach(t -> {
            t.setOrganize(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                String[] split = t.getOrganizeIdTree().split(",");
                if (split.length > 0) {
                    t.setOrganizeIds(Arrays.asList(split));
                } else {
                    t.setOrganizeIds(new ArrayList<>());
                }
            }
        });

        List<OrganizeModel> modelAll = new ArrayList<>();
        modelAll.addAll(models);
        List<SumTree<OrganizeModel>> trees = TreeDotUtils.convertListToTreeDotFilter(modelAll);
        List<OrganizeSelectorVO> listVO = JsonUtil.getJsonToList(trees, OrganizeSelectorVO.class);
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }


    /**
     * 获取组织下拉框列表
     *
     * @param pagination 分页模型
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取组织下拉框列表")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @GetMapping("/SelectorByAuth/{id}")
    public ActionResult<ListVO<OrganizeSelectorByAuthVO>> getSelectorByAuth(Pagination pagination, @PathVariable("id") String id) {
        List<OrganizeEntity> allList = organizeService.getList();
        OrganizeEntity entity = organizeService.getInfo(id);
        allList = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<OrganizeEntity> dataAll = allList;
        if (!StringUtil.isEmpty(pagination.getKeyword())) {
            allList = allList.stream().filter(
                    t -> t.getFullName().contains(pagination.getKeyword()) || t.getEnCode().contains(pagination.getKeyword())
            ).collect(Collectors.toList());
        }
        List<OrganizeEntity> list = JsonUtil.getJsonToList(ListToTreeUtil.treeWhere(allList, dataAll), OrganizeEntity.class);
        list = list.stream().filter(t -> "company".equals(t.getCategory())).collect(Collectors.toList());

        List<OrganizeByAuthModel> models = JsonUtil.getJsonToList(list, OrganizeByAuthModel.class);

        if (!userProvider.get().getIsAdministrator()) {
            // 通过权限转树
            List<OrganizeAdministratorEntity> listss = organizeAdministratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
            Set<String> orgIds = new HashSet<>(16);
            // 判断自己是哪些组织的管理员
            listss.stream().forEach(t-> {
                if (t != null) {
                    if (t.getThisLayerSelect() != null && t.getThisLayerSelect() == 1) {
                        orgIds.add(t.getOrganizeId());
                    }
                    if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                        List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                        orgIds.addAll(underOrganizations);
                    }
                }
            });
            List<String> list1 = new ArrayList<>(orgIds);
            // 得到所有有权限的组织
            List<OrganizeEntity> organizeName = organizeService.getOrganizeName(list1);
            organizeName = organizeName.stream().filter(t->PermissionConst.COMPANY.equals(t.getCategory())).collect(Collectors.toList());
            models = JsonUtil.getJsonToList(organizeName, OrganizeByAuthModel.class);
        }

        // 判断当前编辑的权限时候是否有上级
        if (entity != null) {
            if (models.stream().filter(t -> t.getId().equals(entity.getParentId())).findFirst().orElse(null) == null) {
                OrganizeEntity info = organizeService.getInfo(entity.getParentId());
                if (info != null) {
                    OrganizeByAuthModel jsonToBean = JsonUtil.getJsonToBean(info, OrganizeByAuthModel.class);
                    jsonToBean.setDisabled(true);
                    models.add(jsonToBean);
                }
            }
        }
        List<OrganizeByAuthModel> finalModels = models;
        models.forEach(t -> {
            if (PermissionConst.COMPANY.equals(t.getType())) {
                t.setIcon("icon-ym icon-ym-tree-organization3");
            } else {
                t.setIcon("icon-ym icon-ym-tree-department1");
            }
            // 处理断层
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                String[] split = t.getOrganizeIdTree().split(",");
                List<String> list1 = Arrays.asList(split);
                t.setOrganizeIds(list1);
                t.setOrganize(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
                List<String> list2 = new ArrayList<>(list1);
                Collections.reverse(list2);
                for (String orgId : list2) {
                    OrganizeModel organizeEntity1 = finalModels.stream().filter(organizeEntity -> organizeEntity.getId().equals(orgId)).findFirst().orElse(null);
                    if (organizeEntity1 != null && !organizeEntity1.getId().equals(t.getId())) {
                        t.setParentId(organizeEntity1.getId());
                        String[] split1 = t.getOrganizeIdTree().split(organizeEntity1.getId());
                        if (split1.length > 1) {
                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        break;
                    }
                }
            }
        });
        List<SumTree<OrganizeByAuthModel>> trees = TreeDotUtils.convertListToTreeDot(models);
        List<OrganizeSelectorByAuthVO> listVO = JsonUtil.getJsonToList(trees, OrganizeSelectorByAuthVO.class);
        listVO.forEach(t -> {
            t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
        });
        ListVO<OrganizeSelectorByAuthVO> vo = new ListVO<>();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

    /**
     * 通过部门id获取部门下拉框下拉框
     *
     * @return
     */
    @Operation(summary = "通过部门id获取部门下拉框")
    @Parameters({
            @Parameter(name = "organizeConditionModel", description = "组织id模型", required = true)
    })
    @PostMapping("/OrganizeCondition")
    public ActionResult<ListVO<OrganizeListVO>> organizeCondition(@RequestBody OrganizeConditionModel organizeConditionModel) {
        List<String> idList = new ArrayList<>(16);
        // 获取所有组织
        if (organizeConditionModel.getDepartIds().size() > 0) {
            idList.addAll(organizeConditionModel.getDepartIds());
            organizeConditionModel.getDepartIds().forEach(t -> {
                List<String> underOrganizations = organizeService.getUnderOrganizations(t);
                if (underOrganizations.size() > 0) {
                    idList.addAll(underOrganizations);
                }
            });
        }
        List<OrganizeEntity> listAll = organizeService.getListAll(idList, organizeConditionModel.getKeyword());
        List<OrganizeModel> organizeList = JsonUtil.getJsonToList(listAll, OrganizeModel.class);
        organizeList.forEach(t -> {
            t.setLastFullName(t.getFullName());
            t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
            t.setIcon("department".equals(t.getType()) ? "icon-ym icon-ym-tree-department1" : "icon-ym icon-ym-tree-organization3");
        });
        List<String> collect = organizeList.stream().map(SumTree::getParentId).collect(Collectors.toList());
        List<OrganizeModel> noParentId = organizeList.stream().filter(t->!collect.contains(t.getId()) && !"-1".equals(t.getParentId())).collect(Collectors.toList());
        noParentId.forEach(t->{
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                String[] split = t.getOrganizeIdTree().split(",");
                List<String> list = Arrays.asList(split);
                Collections.reverse(list);
                for (int i = 1; i < list.size(); i++) {
                    String orgId = list.get(i);
                    List<OrganizeModel> collect1 = organizeList.stream().filter(tt -> orgId.equals(tt.getId())).collect(Collectors.toList());
                    if (collect1.size() > 0) {
                        String[] split1 = StringUtil.isNotEmpty(t.getOrganizeIdTree()) ? t.getOrganizeIdTree().split(orgId) : new String[0];
                        if (split1.length > 0) {
                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        t.setParentId(orgId);
                        break;
                    }
                }
            }
        });
        List<SumTree<OrganizeModel>> trees = TreeDotUtils.convertListToTreeDot(organizeList);
        List<OrganizeListVO> listVO = JsonUtil.getJsonToList(trees, OrganizeListVO.class);
        ListVO<OrganizeListVO> vo = new ListVO<>();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

    /**
     * 组织树形
     *
     * @return
     */
    @Operation(summary = "获取组织/公司树形")
    @GetMapping("/Tree")
    public ActionResult<ListVO<OrganizeTreeVO>> tree() {
        List<OrganizeEntity> allList = organizeService.getList();
        List<OrganizeEntity> list = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        list = list.stream().filter(t -> "company".equals(t.getCategory())).collect(Collectors.toList());
        List<OrganizeModel> models = JsonUtil.getJsonToList(list, OrganizeModel.class);
        for (OrganizeModel model : models) {
            model.setIcon("icon-ym icon-ym-tree-organization3");
        }
        List<SumTree<OrganizeModel>> trees = TreeDotUtils.convertListToTreeDot(models);
        List<OrganizeTreeVO> listVO = JsonUtil.getJsonToList(trees, OrganizeTreeVO.class);
        //将子节点全部删除
        Iterator<OrganizeTreeVO> iterator = listVO.iterator();
        while (iterator.hasNext()) {
            OrganizeTreeVO orananizeTreeVO = iterator.next();
            if (!"-1".equals(orananizeTreeVO.getParentId())) {
                iterator.remove();
            }
        }
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

    /**
     * 获取组织信息
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "获取组织信息")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @GetMapping("/{id}")
    public ActionResult<OrganizeInfoVO> info(@PathVariable("id") String id) throws DataException {
        OrganizeEntity entity = organizeService.getInfo(id);
        OrganizeInfoVO vo = JsonUtilEx.getJsonToBeanEx(entity, OrganizeInfoVO.class);
        if (StringUtil.isNotEmpty(entity.getOrganizeIdTree())) {
            String replace = entity.getOrganizeIdTree().replace(entity.getId(), "");
            if (StringUtil.isNotEmpty(replace) && !",".equals(replace)) {
                vo.setOrganizeIdTree(Arrays.asList(replace.split(",")));
            } else {
                vo.setOrganizeIdTree(Arrays.asList(new String[]{"-1"}));
            }
        }
        return ActionResult.success(vo);
    }


    /**
     * 新建组织
     *
     * @param organizeCrForm 新建模型
     * @return
     */
    @OrganizePermission
    @Operation(summary = "新建组织")
    @Parameters({
            @Parameter(name = "organizeCrForm", description = "新建模型", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PostMapping
    public ActionResult create(@RequestBody @Valid OrganizeCrForm organizeCrForm) {
        OrganizeEntity entity = JsonUtil.getJsonToBean(organizeCrForm, OrganizeEntity.class);
        entity.setCategory("company");
        if (organizeService.isExistByFullName(entity, false, false)) {
            return ActionResult.fail("公司名称不能重复");
        }
        if (organizeService.isExistByEnCode(entity.getEnCode(), entity.getId())) {
            return ActionResult.fail("公司编码不能重复");
        }

        // 通过组织id获取父级组织
        String organizeIdTree = getOrganizeIdTree(entity);
        entity.setOrganizeIdTree(organizeIdTree);

        organizeService.create(entity);
        threadPoolExecutor.execute(() -> {
            try{
                //创建组织后判断是否需要同步到企业微信
                synThirdQyService.createDepartmentSysToQy(false, entity, "");
                //创建组织后判断是否需要同步到钉钉
                synThirdDingTalkService.createDepartmentSysToDing(false, entity, "");
            } catch (Exception e) {
                log.error("创建组织后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
            }
        });
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新组织
     *
     * @param id              主键值
     * @param organizeUpForm 实体对象
     * @return
     */
    @OrganizePermission
    @Operation(summary = "更新组织")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true),
            @Parameter(name = "organizeUpForm", description = "实体对象", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PutMapping("/{id}")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid OrganizeUpForm organizeUpForm) {
        List<OrganizeEntity> synList = new ArrayList<>();
        OrganizeEntity entity = JsonUtil.getJsonToBean(organizeUpForm, OrganizeEntity.class);
        OrganizeEntity info = organizeService.getInfo(organizeUpForm.getParentId());
        if (id.equals(entity.getParentId()) || (info != null && info.getOrganizeIdTree() != null && info.getOrganizeIdTree().contains(id))) {
            return ActionResult.fail("当前机构Id不能与父机构Id相同");
        }
        entity.setId(id);
        entity.setCategory("company");
        if (organizeService.isExistByFullName(entity, false, true)) {
            return ActionResult.fail("公司名称不能重复");
        }
        if (organizeService.isExistByEnCode(entity.getEnCode(), entity.getId())) {
            return ActionResult.fail("公司编码不能重复");
        }
        // 通过组织id获取父级组织
        String organizeIdTree = getOrganizeIdTree(entity);
        entity.setOrganizeIdTree(organizeIdTree);
        boolean flag = organizeService.update(id, entity);
        synList.add(entity);

        // 得到所有子组织或部门id
        if (info != null && info.getParentId() != null && !entity.getParentId().equals(info.getParentId())) {
            List<String> underOrganizations = organizeService.getUnderOrganizations(id);
            underOrganizations.forEach(t -> {
                OrganizeEntity info1 = organizeService.getInfo(t);
                if (StringUtil.isNotEmpty(info1.getOrganizeIdTree())) {
                    String organizeIdTrees = getOrganizeIdTree(info1);
                    info1.setOrganizeIdTree(organizeIdTrees);
                    organizeService.update(info1.getId(), info1);
                    synList.add(info1);
                }
            });
        }
        threadPoolExecutor.execute(() -> {
            synList.forEach(t-> {
                try{
                    //修改组织后判断是否需要同步到企业微信
                    synThirdQyService.updateDepartmentSysToQy(false, t, "");
                    //修改组织后判断是否需要同步到钉钉
                    synThirdDingTalkService.updateDepartmentSysToDing(false, t, "");
                } catch (Exception e) {
                    log.error("修改组织后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
                }
            });
        });
        if (!flag) {
            return ActionResult.fail(MsgCode.FA002.get());
        }
        return ActionResult.success(MsgCode.SU004.get());
    }

    /**
     * 删除组织
     *
     * @param orgId 组织主键
     * @return
     */
    @OrganizePermission
    @Operation(summary = "删除组织")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @DeleteMapping("/{id}")
    public ActionResult<String> delete(@PathVariable("id") String orgId) {
        return organizeService.delete(orgId);
    }

    /**
     * 删除部门
     *
     * @param orgId 部门主键
     * @return
     */
    @OrganizePermission
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @Operation(summary = "删除部门")
    @DeleteMapping("/Department/{id}")
    public ActionResult<String> deleteDepartment(@PathVariable("id") String orgId) {
        return organizeService.delete(orgId);
    }

    /**
     * 更新组织状态
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "更新组织状态")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PutMapping("/{id}/Actions/State")
    public ActionResult update(@PathVariable("id") String id) {
        OrganizeEntity organizeEntity = organizeService.getInfo(id);
        if (organizeEntity != null) {
            if ("1".equals(String.valueOf(organizeEntity.getEnabledMark()))) {
                organizeEntity.setEnabledMark(0);
            } else {
                organizeEntity.setEnabledMark(1);
            }
            organizeService.update(organizeEntity.getId(), organizeEntity);
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }


    //---------------------------部门管理--------------------------------------------

    /**
     * 获取部门列表
     *
     * @param companyId 组织id
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "获取部门列表")
    @Parameters({
            @Parameter(name = "companyId", description = "组织id", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @GetMapping("/{companyId}/Department")
    public ActionResult<ListVO<OrganizeDepartListVO>> getListDepartment(@PathVariable("companyId") String companyId, Pagination pagination) {
        List<OrganizeEntity> dataAll = organizeService.getParentIdList(companyId);
        List<String> childId = dataAll.stream().map(t -> t.getId()).collect(Collectors.toList());
        List<OrganizeEntity> data = organizeService.getListAll(childId, pagination.getKeyword());
        //正序显示
        data = data.stream().sorted(Comparator.comparing(OrganizeEntity::getSortCode)).collect(Collectors.toList());
        List<OrganizeModel> models = JsonUtil.getJsonToList(data, OrganizeModel.class);
        if (!userProvider.get().getIsAdministrator()) {
            // 通过权限转树
            List<OrganizeAdministratorEntity> listss = organizeAdministratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
            Set<String> orgIds = new HashSet<>(16);
            // 判断自己是哪些组织的管理员
            listss.stream().forEach(t-> {
                if (t != null) {
                    if (t.getThisLayerSelect() != null && t.getThisLayerSelect() == 1) {
                        orgIds.add(t.getOrganizeId());
                    }
                    if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                        List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                        orgIds.addAll(underOrganizations);
                    }
                }
            });
            List<String> list1 = new ArrayList<>(orgIds);
            List<OrganizeModel> organizeModels = new ArrayList<>(16);

            models.forEach(t -> {
                list1.forEach(tt -> {
                    if (t.getId() != null && t.getId().equals(tt)) {
                        organizeModels.add(t);
                    }
                });
            });
            models = organizeModels;
        }
        // 给部门经理赋值
        for (OrganizeModel model : models) {
            if (!StringUtil.isEmpty(model.getManager())) {
                UserEntity entity = userService.getById(model.getManager());
                model.setManager(entity != null ? entity.getRealName() + "/" + entity.getAccount() : null);
            }
        }
        List<OrganizeDepartListVO> listvo = JsonUtil.getJsonToList(models, OrganizeDepartListVO.class);
        ListVO vo = new ListVO();
        vo.setList(listvo);
        return ActionResult.success(vo);
    }



    /**
     * 获取部门下拉框列表
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取部门下拉框列表")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @GetMapping("/Department/Selector/{id}")
    public ActionResult<ListVO<OrganizeDepartSelectorListVO>> getListDepartment(@PathVariable("id") String id) {
        List<OrganizeEntity> allList = organizeService.getList();
        if (!"0".equals(id)) {
            allList.remove(organizeService.getInfo(id));
        }
        List<OrganizeEntity> data = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<OrganizeModel> models = JsonUtil.getJsonToList(data, OrganizeModel.class);
        for (OrganizeModel model : models) {
            if ("department".equals(model.getType())) {
                model.setIcon("icon-ym icon-ym-tree-department1");
            } else if ("company".equals(model.getType())) {
                model.setIcon("icon-ym icon-ym-tree-organization3");
            }
        }

        models.forEach(t -> {
            t.setOrganize(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                String[] split = t.getOrganizeIdTree().split(",");
                if (split.length > 0) {
                    t.setOrganizeIds(Arrays.asList(split));
                } else {
                    t.setOrganizeIds(new ArrayList<>());
                }
            }
        });

        List<SumTree<OrganizeModel>> trees = TreeDotUtils.convertListToTreeDotFilter(models);
        List<OrganizeDepartSelectorListVO> listVO = JsonUtil.getJsonToList(trees, OrganizeDepartSelectorListVO.class);
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

    /**
     * 获取部门下拉框列表
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取部门下拉框列表")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @GetMapping("/Department/SelectorByAuth/{id}")
    public ActionResult<ListVO<OrganizeSelectorByAuthVO>> getDepartmentSelectorByAuth(@PathVariable("id") String id) {
        List<OrganizeEntity> allList = organizeService.getList();
        OrganizeEntity entity = organizeService.getInfo(id);
        if (!"0".equals(id)) {
            allList.remove(entity);
        }
        List<OrganizeEntity> data = allList.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<OrganizeByAuthModel> models = JsonUtil.getJsonToList(data, OrganizeByAuthModel.class);
        if (!userProvider.get().getIsAdministrator()) {
            // 通过权限转树
            List<OrganizeAdministratorEntity> listss = organizeAdministratorService.getOrganizeAdministratorEntity(userProvider.get().getUserId());
            Set<String> orgIds = new HashSet<>(16);
            // 判断自己是哪些组织的管理员
            listss.stream().forEach(t-> {
                if (t != null) {
                    if (t.getThisLayerSelect() != null && t.getThisLayerSelect() == 1) {
                        orgIds.add(t.getOrganizeId());
                    }
                    if (t.getSubLayerSelect() != null && t.getSubLayerSelect() == 1) {
                        List<String> underOrganizations = organizeService.getUnderOrganizations(t.getOrganizeId());
                        orgIds.addAll(underOrganizations);
                    }
                }
            });
            List<String> list1 = new ArrayList<>(orgIds);
            // 得到所有有权限的组织
            List<OrganizeEntity> organizeName = organizeService.getOrganizeName(list1);
            models = JsonUtil.getJsonToList(organizeName, OrganizeByAuthModel.class);
        }

        // 判断当前编辑的权限时候是否有上级
        if (entity != null) {
            if (models.stream().filter(t -> t.getId().equals(entity.getParentId())).findFirst().orElse(null) == null) {
                OrganizeEntity info = organizeService.getInfo(entity.getParentId());
                if (info != null) {
                    OrganizeByAuthModel jsonToBean = JsonUtil.getJsonToBean(info, OrganizeByAuthModel.class);
                    jsonToBean.setDisabled(true);
                    models.add(jsonToBean);
                }
            }
        }
        List<OrganizeByAuthModel> finalModels = models;
        models.forEach(t -> {
            if (PermissionConst.COMPANY.equals(t.getType())) {
                t.setIcon("icon-ym icon-ym-tree-organization3");
            } else {
                t.setIcon("icon-ym icon-ym-tree-department1");
            }
            // 处理断层
            if (StringUtil.isNotEmpty(t.getOrganizeIdTree())) {
                List<String> list1 = new ArrayList<>();
                String[] split = t.getOrganizeIdTree().split(",");
                list1 = Arrays.asList(split);
                List<String> list = new ArrayList<>(16);
                list1.forEach(orgId -> {
                    if (StringUtil.isNotEmpty(orgId)) {
                        list.add(orgId);
                    }
                });
                t.setOrganizeIds(list);
                t.setOrganize(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
                Collections.reverse(list1);
                for (String orgId : list1) {
                    OrganizeModel organizeEntity1 = finalModels.stream().filter(organizeEntity -> organizeEntity.getId().equals(orgId)).findFirst().orElse(null);
                    if (organizeEntity1 != null && !organizeEntity1.getId().equals(t.getId())) {
                        t.setParentId(organizeEntity1.getId());
                        String[] split1 = t.getOrganizeIdTree().split(organizeEntity1.getId());
                        if (split1.length > 1) {
                            t.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        break;
                    }
                }
            }
        });
        List<SumTree<OrganizeByAuthModel>> trees = TreeDotUtils.convertListToTreeDot(models);
        List<OrganizeSelectorByAuthVO> listVO = JsonUtil.getJsonToList(trees, OrganizeSelectorByAuthVO.class);
        listVO.forEach(t -> {
            t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
        });
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }


    /**
     * 新建部门
     *
     * @param organizeDepartCrForm 新建模型
     * @return
     */
    @OrganizePermission
    @Operation(summary = "新建部门")
    @Parameters({
            @Parameter(name = "organizeDepartCrForm", description = "新建模型", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PostMapping("/Department")
    public ActionResult createDepartment(@RequestBody @Valid OrganizeDepartCrForm organizeDepartCrForm) {
        OrganizeEntity entity = JsonUtil.getJsonToBean(organizeDepartCrForm, OrganizeEntity.class);
        entity.setCategory("department");
        //判断同一个父级下是否含有同一个名称
        if (organizeService.isExistByFullName(entity, false, false)) {
            return ActionResult.fail("部门名称不能重复");
        }
        //判断同一个父级下是否含有同一个编码
        if (organizeService.isExistByEnCode(entity.getEnCode(), entity.getId())) {
            return ActionResult.fail("部门编码不能重复");
        }

        // 通过组织id获取父级组织
        String organizeIdTree = getOrganizeIdTree(entity);
        entity.setOrganizeIdTree(organizeIdTree);

        organizeService.create(entity);
        threadPoolExecutor.execute(() -> {
            try{
                //创建部门后判断是否需要同步到企业微信
                synThirdQyService.createDepartmentSysToQy(false, entity, "");
                //创建部门后判断是否需要同步到钉钉
                synThirdDingTalkService.createDepartmentSysToDing(false, entity, "");
            } catch (Exception e) {
                log.error("创建部门后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
            }
        });
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新部门
     *
     * @param id                    主键值
     * @param oraganizeDepartUpForm 修改模型
     * @return
     */
    @OrganizePermission
    @Operation(summary = "更新部门")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true),
            @Parameter(name = "oraganizeDepartUpForm", description = "修改模型", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PutMapping("/Department/{id}")
    public ActionResult updateDepartment(@PathVariable("id") String id, @RequestBody @Valid OrganizeDepartUpForm oraganizeDepartUpForm) {
        List<OrganizeEntity> synList = new ArrayList<>();
        OrganizeEntity entity = JsonUtil.getJsonToBean(oraganizeDepartUpForm, OrganizeEntity.class);
        OrganizeEntity info = organizeService.getInfo(oraganizeDepartUpForm.getParentId());
        if (id.equals(entity.getParentId()) || (info != null && info.getOrganizeIdTree() != null && info.getOrganizeIdTree().contains(id))) {
            return ActionResult.fail("当前机构Id不能与父机构Id相同");
        }
        entity.setId(id);
        entity.setCategory("department");
        //判断同一个父级下是否含有同一个名称
        if (organizeService.isExistByFullName(entity, false, true)) {
            return ActionResult.fail("部门名称不能重复");
        }
        //判断同一个父级下是否含有同一个编码
        if (organizeService.isExistByEnCode(entity.getEnCode(), entity.getId())) {
            return ActionResult.fail("部门编码不能重复");
        }
        // 通过组织id获取父级组织
        String organizeIdTree = getOrganizeIdTree(entity);
        entity.setOrganizeIdTree(organizeIdTree);
        boolean flag = organizeService.update(id, entity);
        synList.add(entity);

        // 得到所有子组织或部门id
        if (info.getParentId() != null && !entity.getParentId().equals(info.getParentId())) {
            List<String> underOrganizations = organizeService.getUnderOrganizations(id);
            underOrganizations.forEach(t -> {
                OrganizeEntity info1 = organizeService.getInfo(t);
                if (StringUtil.isNotEmpty(info1.getOrganizeIdTree())) {
                    String organizeIdTrees = getOrganizeIdTree(info1);
                    info1.setOrganizeIdTree(organizeIdTrees);
                    organizeService.update(info1.getId(), info1);
                    synList.add(info1);
                }
            });
        }

        threadPoolExecutor.execute(() -> {
            synList.forEach(t-> {
                try{
                    //修改部门后判断是否需要同步到企业微信
                    synThirdQyService.updateDepartmentSysToQy(false, organizeService.getInfo(id), "");
                    //修改部门后判断是否需要同步到钉钉
                    synThirdDingTalkService.updateDepartmentSysToDing(false, organizeService.getInfo(id), "");
                } catch (Exception e) {
                    log.error("修改部门后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
                }
            });
        });
        if (flag == false) {
            return ActionResult.fail(MsgCode.FA002.get());
        }
        return ActionResult.success(MsgCode.SU004.get());
    }



    /**
     * 更新部门状态
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "更新部门状态")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @PutMapping("/Department/{id}/Actions/State")
    public ActionResult updateDepartment(@PathVariable("id") String id) {
        OrganizeEntity organizeEntity = organizeService.getInfo(id);
        if (organizeEntity != null) {
            if ("1".equals(String.valueOf(organizeEntity.getEnabledMark()))) {
                organizeEntity.setEnabledMark(0);
            } else {
                organizeEntity.setEnabledMark(1);
            }
            organizeService.update(organizeEntity.getId(), organizeEntity);
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.fail(MsgCode.FA002.get());
    }

    /**
     * 获取部门信息
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "获取部门信息")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission(value = {"permission.organize"})
    @GetMapping("/Department/{id}")
    public ActionResult<OrganizeDepartInfoVO> infoDepartment(@PathVariable("id") String id) throws DataException {
        OrganizeEntity entity = organizeService.getInfo(id);
        OrganizeDepartInfoVO vo = JsonUtilEx.getJsonToBeanEx(entity, OrganizeDepartInfoVO.class);
        List<String> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(entity.getOrganizeIdTree())) {
            String[] split = entity.getOrganizeIdTree().split(",");
            if (split.length > 1) {
                for (int i = 0; i < split.length - 1; i++) {
                    list.add(split[i]);
                }
            }
        }
        vo.setOrganizeIdTree(list);
        return ActionResult.success(vo);
    }

    /**
     * 获取父级组织id
     *
     * @param entity
     * @return
     */
    private String getOrganizeIdTree(OrganizeEntity entity) {
        List<String> list = new ArrayList<>();
        organizeService.getOrganizeIdTree(entity.getParentId(), list);
        // 倒叙排放
        Collections.reverse(list);
        StringBuilder organizeIdTree = new StringBuilder();
        for (String organizeParentId : list) {
            organizeIdTree.append("," + organizeParentId);
        }
        String organizeParentIdTree = organizeIdTree.toString();
        if (StringUtil.isNotEmpty(organizeParentIdTree)) {
            organizeParentIdTree = organizeParentIdTree.replaceFirst(",", "");
        }
        return organizeParentIdTree;
    }

//    /**
//     * 获取父级组织id
//     *
//     * @param entity
//     * @return
//     */
//    private String getOrganizeIdTrees(OrganizeEntity entity) {
//        List<String> list = new ArrayList<>();
//        organizeService.getOrganizeIdTree(entity.getId(), list);
//        // 倒叙排放
//        Collections.reverse(list);
//        StringBuffer organizeIdTree = new StringBuffer();
//        for (String organizeParentId : list) {
//            organizeIdTree.append("," + organizeParentId);
//        }
//        String organizeParentIdTree = organizeIdTree.toString();
//        if (StringUtil.isNotEmpty(organizeParentIdTree)) {
//            organizeParentIdTree = organizeParentIdTree.replaceFirst(",", "");
//        }
//        return organizeParentIdTree;
//    }
//
//    @GetMapping("/aaa")
//    public void aaa() {
//        List<OrganizeEntity> list = organizeService.getList();
//        list.forEach(t->{
//            String organizeIdTree = getOrganizeIdTrees(t);
//            t.setOrganizeIdTree(organizeIdTree);
//            organizeService.updateById(t);
//        });
//    }

    /**
     * 获取默认当前值部门ID
     *
     * @param organizeConditionModel 参数
     * @return 执行结构
     * @throws DataException ignore
     */
    @Operation(summary = "获取默认当前值部门ID")
    @Parameters({
            @Parameter(name = "organizeConditionModel", description = "参数", required = true)
    })
    @PostMapping("/getDefaultCurrentValueDepartmentId")
    public ActionResult<?> getDefaultCurrentValueDepartmentId(@RequestBody OrganizeConditionModel organizeConditionModel) throws DataException {
        String departmentId = organizeService.getDefaultCurrentValueDepartmentId(organizeConditionModel);
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("departmentId", departmentId);
        return ActionResult.success("查询成功", dataMap);
    }

}
