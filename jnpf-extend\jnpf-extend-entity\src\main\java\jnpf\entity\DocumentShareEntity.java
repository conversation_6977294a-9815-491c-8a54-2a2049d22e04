package jnpf.entity;

import jnpf.base.entity.SuperBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 知识文档共享
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("ext_documentshare")
public class DocumentShareEntity extends SuperBaseEntity.SuperTBaseEntity<String> {

    /**
     * 文档主键
     */
    @TableField("F_DOCUMENTID")
    private String documentId;

    /**
     * 共享人员
     */
    @TableField("F_SHAREUSERID")
    private String shareUserId;

    /**
     * 共享时间
     */
    @TableField("F_SHARETIME")
    private Date shareTime;

}
