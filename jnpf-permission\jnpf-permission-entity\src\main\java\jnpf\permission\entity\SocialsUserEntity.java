package jnpf.permission.entity;

import jnpf.base.entity.SuperBaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 流程设计
 *
 * <AUTHOR>
 * @version V3.4.2
 * @copyright 引迈信息技术有限公司
 * @date 2022/7/14 9:28:32
 */
@Data
@TableName("base_socialsusersentity")
public class SocialsUserEntity extends SuperBaseEntity.SuperCDBaseEntity<String> {

    /**
     * 系统用户id
     */
    @TableField("F_USERID")
    private String userId;
    /**
     * 第三方类型
     */
    @TableField("F_SOCIALTYPE")
    private String socialType;
    /**
     * 第三方uuid
     */
    @TableField("F_SOCIALID")
    private String socialId;
    /**
     * 第三方账号
     */
    @TableField("F_SOCIALNAME")
    private String socialName;

    /**
     * 描述
     */
    @TableField("F_DESCRIPTION")
    private String description;

}
