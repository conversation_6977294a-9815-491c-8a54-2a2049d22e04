package jnpf.permission.model.user.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

/**
 * 类功能
 *
 * <AUTHOR> YanYu
 * @version V3.2.0
 * @copyright 引迈信息技术有限公司
 * @date 2022/1/28
 */
@Data
public class UserSettingForm {

    @Schema(description = "主要类型")
    private String majorType;
    @Schema(description = "主要Id")
    private String majorId;

    @Schema(description = "菜单类型")
    private Integer menuType;

}
