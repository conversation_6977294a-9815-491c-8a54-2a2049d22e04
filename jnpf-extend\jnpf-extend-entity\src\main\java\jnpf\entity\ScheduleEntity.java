package jnpf.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 日程安排
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("ext_schedule")
public class ScheduleEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    /**
     * 日程标题
     */
    @TableField("F_TITLE")
    private String title;

    /**
     * 日程内容
     */
    @TableField("F_CONTENT")
    private String content;

    /**
     * 日程颜色
     */
    @TableField("F_COLOUR")
    private String colour;

    /**
     * 颜色样式
     */
    @TableField("F_COLOURCSS")
    private String colourCss;

    /**
     * 开始时间
     */
    @TableField("F_STARTTIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("F_ENDTIME")
    private Date endTime;

    /**
     * App提醒
     */
    @TableField("F_APPALERT")
    private Integer appAlert;

    /**
     * 提醒设置
     */
    @TableField("F_EARLY")
    private Integer early;

    /**
     * 邮件提醒
     */
    @TableField("F_MAILALERT")
    private Integer mailAlert;

    /**
     * 微信提醒
     */
    @TableField("F_WECHATALERT")
    private Integer weChatAlert;

    /**
     * 短信提醒
     */
    @TableField("F_MOBILEALERT")
    private Integer mobileAlert;

    /**
     * 系统提醒
     */
    @TableField("F_SYSTEMALERT")
    private Integer systemAlert;

}
