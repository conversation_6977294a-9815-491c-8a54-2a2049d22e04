package jnpf.message.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 消息发送配置表
 *
 * @版本： V3.2.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2022-08-19
 */
@Data
@TableName("base_message_send_config")
public class SendMessageConfigEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    @TableField("F_FULLNAME")
    private String fullName;

    @TableField("F_ENCODE")
    private String enCode;

//    @TableField("F_MESSAGETYPE")
//
//    private String messageType;

    @TableField("F_TEMPLATETYPE")
    private String templateType;

    @TableField("F_MESSAGESOURCE")
    private String messageSource;

    @TableField("F_USEDID")
    private String usedId;

}
