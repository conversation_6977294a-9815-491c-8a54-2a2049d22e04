package jnpf.permission.model.position;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/12 15:31
 */
@Data
public class PositionSelectorVO {
    private String id;
    @Schema(description = "父级ID")
    private String  parentId;
    @Schema(description = "名称")
    private String  fullName;
    @Schema(description = "是否有下级菜单")
    private Boolean hasChildren;
    @Schema(description = "状态")
    private Integer enabledMark;
    @Schema(description = "下级菜单列表")
    private List<PositionSelectorVO> children;
    @JSONField(name="category")
    private String  type;
    @Schema(description = "图标")
    private String icon;


    private String organize;
    @Schema(description = "组织id树")
    private List<String> organizeIds;
}
