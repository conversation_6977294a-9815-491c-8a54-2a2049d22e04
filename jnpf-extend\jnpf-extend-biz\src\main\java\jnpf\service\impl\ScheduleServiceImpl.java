package jnpf.service.impl;

import jnpf.base.service.SuperServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jnpf.entity.ScheduleEntity;
import jnpf.mapper.ScheduleMapper;
import jnpf.service.ScheduleService;
import jnpf.util.DateUtil;
import jnpf.util.RandomUtil;
import jnpf.util.UserProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 日程安排
 *
 * @copyright 引迈信息技术有限公司
 * <AUTHOR>
 * @version V3.1.0
 * @date 2019年9月26日 上午9:18
 */
@Service
public class ScheduleServiceImpl extends SuperServiceImpl<ScheduleMapper, ScheduleEntity> implements ScheduleService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public List<ScheduleEntity> getList(String startTime, String endTime) {
        QueryWrapper<ScheduleEntity> queryWrapper = new QueryWrapper<>();
        Date startTimes = DateUtil.stringToDates(startTime);
        Date endTimes = DateUtil.stringToDates(endTime);
        queryWrapper.lambda().eq(ScheduleEntity::getCreatorUserId,userProvider.get().getUserId())
                .ge(ScheduleEntity::getStartTime,startTimes)
                .le(ScheduleEntity::getEndTime,endTimes)
                .orderByAsc(ScheduleEntity::getCreatorTime);
        return this.list(queryWrapper);
    }

    @Override
    public ScheduleEntity getInfo(String id) {
        QueryWrapper<ScheduleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScheduleEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void delete(ScheduleEntity entity) {
        this.removeById(entity.getId());
    }

    @Override
    public void create(ScheduleEntity entity) {
        entity.setId(RandomUtil.uuId());
        entity.setCreatorUserId(userProvider.get().getUserId());
        this.save(entity);
    }

    @Override
    public boolean update(String id, ScheduleEntity entity) {
        entity.setId(id);
        entity.setLastModifyTime(new Date());
        entity.setLastModifyUserId(userProvider.get().getUserId());
        return this.updateById(entity);
    }
}
