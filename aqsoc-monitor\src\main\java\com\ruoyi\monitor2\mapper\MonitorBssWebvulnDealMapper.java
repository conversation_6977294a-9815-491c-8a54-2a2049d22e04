package com.ruoyi.monitor2.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * web漏洞事件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
public interface MonitorBssWebvulnDealMapper {
    /**
     * 查询web漏洞事件
     *
     * @param id web漏洞事件主键
     * @return web漏洞事件
     */
    public MonitorBssWebvulnDeal selectMonitorBssWebvulnDealById(Long id);

    List<MonitorBssWebvulnDeal> getMonitorBssWebvulnDealList(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    List<MonitorBssWebvulnDeal> findMonitorBssWebvulnDealList(MonitorBssWebvulnDeal monitorBssWebvulnDeal);
    /**
     * 批量查询web漏洞事件
     *
     * @param ids web漏洞事件主键集合
     * @return web漏洞事件集合
     */
    public List<MonitorBssWebvulnDeal> selectMonitorBssWebvulnDealByIds(Long[] ids);

    List<MonitorBssWebvulnDeal> getMonitorBssWebVulnDealDetail(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    /**
     * 查询web漏洞事件列表
     *
     * @param monitorBssWebvulnDeal web漏洞事件
     * @return web漏洞事件集合
     */
    @DataScope(deptAlias = "sd")
    public List<MonitorBssWebvulnDeal> selectMonitorBssWebvulnDealList(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    int selectListCount(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    /**
     * 新增web漏洞事件
     *
     * @param monitorBssWebvulnDeal web漏洞事件
     * @return 结果
     */
    public int insertMonitorBssWebvulnDeal(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    /**
     * 修改web漏洞事件
     *
     * @param monitorBssWebvulnDeal web漏洞事件
     * @return 结果
     */
    public int updateMonitorBssWebvulnDeal(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    public int updateBatchMonitorBssWebvulnDeal(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    /**
     * 删除web漏洞事件
     *
     * @param id web漏洞事件主键
     * @return 结果
     */
    public int deleteMonitorBssWebvulnDealById(Long id);

    /**
     * 批量删除web漏洞事件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorBssWebvulnDealByIds(Long[] ids);

    /**
     * 统计事件等级
     * @param monitorBssWebvulnDeal
     * @return
     */
    List<HashMap> getDealSeverity(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    List<JSONObject> getWebvulnCount(@Param("url") List<String> url);


    List<MonitorBssWebvulnDeal> getWebGapLinkBusinessList(MonitorBssWebvulnDeal monitorBssWebvulnDeal);

    void batchUpdateHandleState(@Param("list") List<MonitorBssWebvulnDeal> handleList);

    @DataScope(deptAlias = "sd")
    public List<HashMap> getRickLevelStat(TblThreatenAlarm tblThreatenAlarm);
    public List<HashMap> getHandleStateStat();
    public List<HashMap> getWebVulnTypeList();

    List<MonitorBssWebvulnDeal> selectNoSyncList();

    int countWebVulnDealNum(OverviewParams params);

    List<JSONObject> getStatisticsOnWebVulnerabilityTypes(MonitorBssVulnDeal monitorBssVulnDeal);

    String getFindTheFirstMonitorBssWebvulnDealId();
}
