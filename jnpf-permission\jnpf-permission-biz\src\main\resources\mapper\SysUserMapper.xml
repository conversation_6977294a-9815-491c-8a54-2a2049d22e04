<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.permission.mapper.RuoYiUserMapper">

	<resultMap type="jnpf.model.SysUser" id="SysUserResult">
		<id property="userId" column="user_id"/>
		<result property="deptId" column="dept_id"/>
		<result property="userName" column="user_name"/>
		<result property="nickName" column="nick_name"/>
		<result property="email" column="email"/>
		<result property="phonenumber" column="phonenumber"/>
		<result property="sex" column="sex"/>
		<result property="avatar" column="avatar"/>
		<result property="password" column="password"/>
		<result property="status" column="status"/>
		<result property="delFlag" column="del_flag"/>
		<result property="loginIp" column="login_ip"/>
		<result property="loginDate" column="login_date"/>
		<result property="passTime" column="passtime"/>
		<result property="createBy" column="create_by"/>
		<result property="createTime" column="create_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="remark" column="remark"/>
	</resultMap>
	
	<select id="listSysUserByRole" parameterType="Long" resultMap="SysUserResult">
		SELECT u.* FROM sys_user u LEFT JOIN sys_user_role ur ON u.user_id=ur.user_id
		WHERE 1=1 AND ur.role_id in
		<foreach item="item" index="index" collection="roleIds" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND u.del_flag = '0'
	</select>

</mapper> 