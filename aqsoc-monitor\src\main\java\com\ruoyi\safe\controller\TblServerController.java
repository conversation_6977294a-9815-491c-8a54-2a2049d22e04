package com.ruoyi.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeSoftwareDetails;
import com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeSoftwareDetailsResultMapper;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.TblDeploy;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.safe.service.IAssetVulnerabilityStatsService;
import com.ruoyi.safe.service.IMonitorHandleService;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblServerService;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.safe.domain.dto.QueryDeptServerCountDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 服务器/存储设备Controller
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@RestController
@RequestMapping("/safe/server")
public class TblServerController extends BaseController
{
    @Autowired
    private ITblServerService tblServerService;
    @Autowired
    private ICountByDictService countByDictService;
    @Autowired
    private IMonitorHandleService monitorHandleService;
    @Autowired
    private IAssetVulnerabilityStatsService assetVulnerabilityStatsService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;

    @Resource
    private FfsafeSoftwareDetailsResultMapper ffsafeSoftwareDetailsResultMapper;

    /**
     * 查询服务器/存储设备列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:server:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblServer tblServer)
    {
        //PageUtils.startPage("update_time desc");
        startPage();
        List<TblServer> list = tblServerService.selectTblServerList2(tblServer);
        if (CollUtil.isNotEmpty(list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("2");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName")) ||
                                "硬件/软件概况信息".equals(jsonObject.getString("formName")) ||
                                "位置信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                List<JSONObject> networkInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "网络信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    TblServer tblServerInfo = list.get(i);
                    List<TblNetworkIpMac> ipMacArr = tblServerInfo.getIpMacArr();
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        basicInformationList.forEach(basicInformation -> {
                            String fieldKey = basicInformation.getString("fieldKey");
                            if ("vendor".equals(fieldKey)){
                                if (StrUtil.isNotBlank(tblServerInfo.getVendorName())){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            }else if ("dbId".equals(fieldKey)){
                                if (tblServerInfo.getDbsystem() != null){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            } else if ("mdId".equals(fieldKey)) {
                                if (tblServerInfo.getMdsystem() != null){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            } else if ("optId".equals(fieldKey)){
                                if (tblServerInfo.getOptsystem() != null){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            }else {
                                Object fieldValue = ReflectUtil.getFieldValue(tblServerInfo, fieldKey);
                                if (fieldValue != null && !"".equals(fieldValue)){
                                    numerator.getAndSet(numerator.get() + 1);
                                }else {
                                    System.out.println(fieldKey);
                                }
                            }
                        });
                    }
                    if (CollUtil.isNotEmpty(networkInformationList)){
                        networkInformationList.forEach(networkInformation -> {
                            String fieldKey = networkInformation.getString("fieldKey");
                            if ("exposedIp".equals(fieldKey)){
                                if (StrUtil.isNotBlank(tblServerInfo.getExposedIp())){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            }else if ("isMainIp".equals(fieldKey)){
                                numerator.getAndSet(numerator.get() + 1);
                            } else {
                                if (CollUtil.isNotEmpty(ipMacArr)){
                                    for (TblNetworkIpMac tblNetworkIpMac : ipMacArr){
                                        Object fieldValue = ReflectUtil.getFieldValue(tblNetworkIpMac, fieldKey);
                                        if (fieldValue == null || "".equals(fieldValue)){
                                            break;
                                        } else {
                                            numerator.getAndSet(numerator.get() + 1);
                                        }
                                    }
                                }
                            }
                        });
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    double formattedCompletionRate = bd.doubleValue();
                    tblServerInfo.setCompleteness(formattedCompletionRate);
                }
            }
        }
        // 为服务器列表添加统计信息
        assetVulnerabilityStatsService.batchEnrichServersWithStats(list);

        return getDataTable(list);
    }

    /**
     * 导出服务器/存储设备列表
     */
    @PreAuthorize("@ss.hasPermi('safe:server:export')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblServer tblServer)
    {
        List<TblServer> list = tblServerService.selectTblServerList2(tblServer);
        list.forEach(e -> {
            if (StringUtils.isNotBlank(e.getLaseScanState())) {
                e.setState(e.getLaseScanState());
            }
        });
        ExcelUtil<TblServer> util = new ExcelUtil<TblServer>(TblServer.class);
        util.exportExcel(response, list, "服务器存储设备");
    }

    /**
     * 获取服务器/存储设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:server:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblServerService.selectTblServerByAssetId(assetId));
    }

    /**
     * 新增服务器/存储设备
     */
    @PreAuthorize("@ss.hasPermi('safe:server:add')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblServer tblServer)
    {

        AjaxResult ajaxResult = toAjax(tblServerService.insertTblServer(tblServer));
        ajaxResult.put("data",tblServer);
        return ajaxResult;
    }

    /**
     * 新增服务器/存储设备
     */
    @PreAuthorize("@ss.hasPermi('safe:server:add')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping("add2")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add2(@RequestBody TblServer tblServer)
    {
        if (CollUtil.isNotEmpty(tblServer.getIpMacArr())){
            List<String> collect = tblServer.getIpMacArr().stream().map(TblNetworkIpMac::getIpv4).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)){
                monitorHandleService.deleteByIp(collect);
            }
        }
        AjaxResult ajaxResult = toAjax(tblServerService.insertTblServer2(tblServer));
        ajaxResult.put("data",tblServer);
        return ajaxResult;
    }

    /**
     * 修改服务器/存储设备
     */
    @PreAuthorize("@ss.hasPermi('safe:server:edit')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblServer tblServer)
    {
        return toAjax(tblServerService.updateTblServer(tblServer));
    }

    @PreAuthorize("@ss.hasPermi('safe:server:edit')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping("/unbind")
    public AjaxResult unbind(@RequestBody TblServer tblServer)
    {
        return toAjax(tblServerService.unbind(tblServer));
    }

    /**
     * 修改服务器/存储设备
     */
    @PreAuthorize("@ss.hasPermi('safe:server:edit')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping("update2")
    public AjaxResult edit2(@RequestBody TblServer tblServer)
    {
        return toAjax(tblServerService.updateTblServer2(tblServer));
    }

    /**
     * 删除服务器/存储设备
     */
    @PreAuthorize("@ss.hasPermi('safe:server:remove')")
    @Log(title = "服务器/存储设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblServerService.deleteTblServerByAssetIds(assetIds));
    }

    /**
     * 导入服务器
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/12 15:33
     * @return null
     **/
    @Log(title = "服务器/存储设备", businessType = BusinessType.IMPORT)
    @PostMapping("/importServer")
    public AjaxResult importServer(MultipartFile file) throws Exception {
        ExcelUtil<TblServer> util = new ExcelUtil<>(TblServer.class);
        List<TblServer> serverList = util.importExcel(file.getInputStream(), 1);
        if (CollUtil.isNotEmpty(serverList)){
            AtomicInteger i = new AtomicInteger();
            serverList.forEach(e -> {
                if (StringUtils.isBlank(e.getAssetCode())){
                    e.setAssetCode("FWQ"+ DateUtil.format(new Date(), "yyyyMMddHHmmss"+i));
                    i.set(i.get() + 1);
                }
            });
        }
        String message = tblServerService.importServer(serverList);
        return AjaxResult.success(message);
    }

    /**
     * 下载 服务器/存储设备 模板
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/12 16:59
     * @return com.ruoyi.common.core.domain.AjaxResult
     **/
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TblServer> util = new ExcelUtil<TblServer>(TblServer.class);
        util.hideColumn("state", "lastScanTime", "vulnNum", "vnlnUpdateTime");
        List<TblServer> list = new ArrayList<>();
        TblServer server = new TblServer();
        server.setAssetCode("举例：07910616983M");
        server.setAssetName("资产数据服务器07");
        server.setAssetType(129L);
        server.setDegreeImportance("5");
        server.setDomainName("视频专网");
        server.setIp("*************");
        server.setCpuFrame("Mips");
        server.setOptsystem(new TblDeploy().setProcName("CentOS 6 64bit"));
        server.setDbsystem(new TblDeploy().setProcName("MySQL"));
        server.setMdsystem(new TblDeploy().setProcName("Nginx"));
        server.setMac("A0:01:0E:4W:9O:1X");
        server.setVendorName("某某技术有限公司");
        server.setFacilityManufacturerName("某某技术有限公司");
        server.setFacilityType("WY IDJ008");
        server.setMaintainUnitName("某某技术有限公司");
        server.setIsVirtual("Y");
        server.setBaseAssetName("数据库服务器1");
        server.setDetails("专业数据库");
        server.setLocationName("南昌市红谷滩办公大楼3楼机房A1机柜1U");
        server.setLocationDetail("南昌市红谷滩");
        server.setTags("西湖区数据库,东湖区数据库");
        server.setRemark("需要定期维护与数据扫码");
        list.add(server);
        util.exportExcel(response, list,"服务器模板",  "注意事项：\n" +
                "1. 字段前有*则属于必填项，如未填写则无法导入，其余为非必填选项。\n" +
                "2. 因为没有统一规范以下字段：所属部门、重要程度、网络区域、操作系统、中间件、数据库、供应商、设备厂商、维保单位、承载设备、所在位置，需要参考系统中的“选项数据”进行填写，如系统中没有需在系统中新增，否则无法导入数据。");
    }

    /**
     * 根据不同的字典类型统计业务系统数量
     * @param dictType
     * @return
     */
    @GetMapping("/getServerCountByDict")
    public AjaxResult getServerCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"server");
        return AjaxResult.success(countByDict);
    }

    @GetMapping(value = "/getEdrByAssetId/{assetId}")
    public AjaxResult getEdrInfo(@PathVariable("assetId") Long assetId) throws IOException {
        return AjaxResult.success(tblServerService.selectEdrByAssetId(assetId));
    }

    @GetMapping("/edrSoftware")
    public TableDataInfo edrSoftware(FfsafeSoftwareDetails ffsafeSoftwareDetails)
    {
        startPage();
        List<FfsafeSoftwareDetails> list = ffsafeSoftwareDetailsResultMapper.selectFfsafeSoftwareDetailsList(ffsafeSoftwareDetails);
        return getDataTable(list);
    }

    /**
     * 获取部门服务器统计
     */
    @GetMapping("/getDepts")
    public AjaxResult getDepts() {
        QueryDeptServerCountDto queryCountDto = new QueryDeptServerCountDto();
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        return AjaxResult.success(tblServerService.getDeptServerCount(queryCountDto));
    }
}
