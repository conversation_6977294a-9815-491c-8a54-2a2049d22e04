package jnpf.message.entity;

import jnpf.base.entity.SuperEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonProperty;
/**
 *
 * 消息模板参数表
 * @版本： V3.2.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2022-08-18
 */
@Data
@TableName("base_message_template_param")
public class TemplateParamEntity extends SuperEntity<String>  {

    /** 消息模板id **/
    @TableField("F_TEMPLATEID")
    private String templateId;

    /** 参数 **/
    @TableField("F_FIELD")
    private String field;

    /** 参数说明 **/
    @TableField("F_FIELDNAME")
    private String fieldName;

    /**
     * 状态
     */
    @TableField("F_ENABLEDMARK")
    private Integer enabledMark;

}
