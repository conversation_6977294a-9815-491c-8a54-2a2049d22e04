package jnpf.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.PaginationTime;
import jnpf.base.vo.PaginationVO;
import jnpf.constant.MsgCode;
import jnpf.entity.LogEntity;
import jnpf.model.*;
import jnpf.service.LogService;
import jnpf.util.JsonUtil;
import jnpf.util.StringUtil;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月27日 上午9:18
 */
@Tag(name = "系统日志", description = "Log")
@RestController
@RequestMapping("/api/system/Log")
public class LogController extends SuperController<LogService, LogEntity> {

    @Autowired
    private LogService logService;


    /**
     * 获取系统日志信息
     *
     * @param category 主键值分类 1：登录日志，2.访问日志，3.操作日志，4.异常日志，5.请求日志
     * @return
     */
    @Operation(summary = "获取系统日志列表")
    @Parameters({
            @Parameter(name = "category", description = "分类", required = true)
    })
    @SaCheckPermission("system.log")
    @GetMapping("/{category}")
    public ActionResult getInfoList(@PathVariable("category") String category, PaginationLogModel paginationTime) {
        if (StringUtil.isEmpty(category) || !StringUtil.isNumeric(category)) {
            return ActionResult.fail("获取失败");
        }
        List<LogEntity> list = logService.getList(Integer.parseInt(category), paginationTime);
        PaginationVO paginationVO = JsonUtil.getJsonToBean(paginationTime, PaginationVO.class);
        int i = Integer.parseInt(category);
        switch (i) {
            case 1:
                List<LoginLogVO> loginLogVOList = JsonUtil.getJsonToList(list, LoginLogVO.class);
                return ActionResult.page(loginLogVOList, paginationVO);
            case 3:
                List<HandleLogVO> handleLogVOList = JsonUtil.getJsonToList(list, HandleLogVO.class);
                for (int j = 0; j < handleLogVOList.size(); j++) {
                    handleLogVOList.get(j).setJson(list.get(j).getJsons());
                }
                return ActionResult.page(handleLogVOList, paginationVO);
            case 4:
                List<ErrorLogVO> errorLogVOList = JsonUtil.getJsonToList(list, ErrorLogVO.class);
                for (int j = 0; j < errorLogVOList.size(); j++) {
                    errorLogVOList.get(j).setJson(list.get(j).getJsons());
                }
                return ActionResult.page(errorLogVOList, paginationVO);
            case 5:
                List<RequestLogVO> requestLogVOList = JsonUtil.getJsonToList(list, RequestLogVO.class);
                return ActionResult.page(requestLogVOList, paginationVO);
            default:
                return ActionResult.fail("获取失败");
        }
    }

    /**
     * 批量删除系统日志
     *
     * @param logDelForm 批量删除日志模型
     * @return
     */
    @Operation(summary = "批量删除系统日志")
    @Parameters({
            @Parameter(name = "logDelForm", description = "批量删除日志模型", required = true)
    })
    @SaCheckPermission("system.log")
    @DeleteMapping
    public ActionResult delete(@RequestBody LogDelForm logDelForm) {
        boolean flag = logService.delete(logDelForm.getIds());
        if (flag == false) {
            return ActionResult.fail(MsgCode.FA003.get());
        }
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 一键清空操作日志
     *
     * @param type 分类
     * @return
     */
    @Operation(summary = "一键清空操作日志")
    @Parameters({
            @Parameter(name = "type", description = "分类", required = true)
    })
    @SaCheckPermission("system.log")
    @DeleteMapping("/{type}")
    public ActionResult deleteHandelLog(@PathVariable("type") String type) {
        logService.deleteHandleLog(type);
        return ActionResult.success(MsgCode.SU005.get());
    }

    /**
     * 获取菜单名
     *
     * @return
     */
    @Operation(summary = "获取菜单名")
    @SaCheckPermission("system.log")
    @GetMapping("/ModuleName")
    public ActionResult<List<Map<String, String>>> moduleName() {
        List<Map<String, String>> list = new ArrayList<> (16);
        Set<String> set = logService.queryList();
        for (String moduleName : set) {
            Map<String, String> map = new HashedMap<>(1);
            map.put("moduleName", moduleName);
            list.add(map);
        }
        return ActionResult.success(list);
    }

}
