package jnpf.permission.constant;

/**
 * 类功能
 *
 * <AUTHOR> YanYu
 * @version V3.2.0
 * @copyright 引迈信息技术有限公司
 * @date 2022/2/14
 */
public class AuthorizeConst {

    public final static String USER = "User";
    /**
     * 岗位 权限标识
     */
    public final static String POSITION = "Position";
    /**
     * 角色 权限标识
     */
    public final static String ROLE = "Role";
    /**
     * 按钮 权限标识
     */
    public final static String BUTTON = "button";
    /**
     * 菜单 权限标识
     */
    public final static String MODULE = "module";
    /**
     * 列表 权限标识
     */
    public final static String COLUMN = "column";
    /**
     * 数据 权限标识
     */
    public final static String RESOURCE = "resource";
    /**
     * 表单 权限标识
     */
    public final static String FROM = "form";
    /**
     * 系统、子系统
     */
    public final static String SYSTEM = "system";

    /**
     * 权限标识集合
     */
    public final static String[] TYPES = {USER, POSITION, ROLE, BUTTON, MODULE, COLUMN, RESOURCE, FROM};

}
