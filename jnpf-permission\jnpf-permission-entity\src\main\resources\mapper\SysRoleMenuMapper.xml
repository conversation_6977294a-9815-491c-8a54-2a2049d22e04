<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jnpf.permission.mapper.SysRoleMenuMapper">

    <resultMap id="BaseResultMap" type="jnpf.base.entity.RuoYiSysRoleMenuEntity">
            <id property="roleId" column="role_id" jdbcType="BIGINT"/>
            <id property="menuId" column="menu_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,menu_id
    </sql>
</mapper>
