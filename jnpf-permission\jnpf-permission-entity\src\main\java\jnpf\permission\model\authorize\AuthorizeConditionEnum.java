package jnpf.permission.model.authorize;

/**
 * 数据权限过滤条件字段
 * <AUTHOR>
 * @version V3.2
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date  2021/10/9
 */
public enum AuthorizeConditionEnum {
	/**
	 * 任意文本
	 */
	TEXT("text"),
	/**
	 * 当前组织
	 */
	ORGANIZE("@organizeId"),
	/**
	 * 当前组织及子组织
	 */
	ORGANIZEANDUNDER("@organizationAndSuborganization"),
	/**
	 * 当前用户
	 */
	USER("@userId"),
	/**
	 * 当前用户及下属
	 */
	USERANDUNDER("@userAraSubordinates"),
	/**
	 * 当前分管组织
	 */
	BRANCHMANAGEORG("@branchManageOrganize"),

	/**
	 * 当前分管组织及子组织
	 */
	BRANCHMANAGEORGANIZEUNDER("@branchManageOrganizeAndSub")
	;
	private String condition;

	AuthorizeConditionEnum(String condition) {
		this.condition = condition;
	}

	public String getCondition() {
		return condition;
	}

	public void setCondition(String condition) {
		this.condition = condition;
	}
}
