package jnpf.onlinedev.model;

import lombok.Data;

/**
 * 在线开发常用常量
 */
@Data
public class OnlineDevData {

    /**
     * 用于判断是否是有表数据
     */
    public static final String TABLE_CONST = "[]";

    /**
     * 详情id副本
     */
    public static final String INFO_ID = "_id";


    /**
     * 列表分组类型3
     */
    public static final Integer TYPE_THREE_COLUMNDATA = 3;

    /**
     * 树形列表类型5
     */
    public static final Integer TYPE_FIVE_COLUMNDATA = 5;


    /**
     * 禁用
     */
    public static final Integer STATE_DISABLE = 0;
    /**
     * 启用
     */
    public static final Integer STATE_ENABLE = 1;
    /**
     * 系统表单
     */
    public static final Integer FORM_TYPE_SYS = 1;
    /**
     * 自定义表单
     */
    public static final Integer FORM_TYPE_DEV = 2;
    /**
     * 发起流程类型
     */
    public static final Integer FLOW_TYPE_FLOW = 0;
    /**
     * 功能流程类型
     */
    public static final Integer FLOW_TYPE_DEV = 1;
    /**
     * 默认分类编码encode
     */
    public static final String DEFAULT_CATEGATY_ENCODE = "default";




}
