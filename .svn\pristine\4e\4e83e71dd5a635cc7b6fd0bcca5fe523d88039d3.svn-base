package com.ruoyi.safe.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.domain.dto.QueryDeptServerCountDto;
import com.ruoyi.safe.domain.dto.QueryDeptTerminalCountDto;
import com.ruoyi.safe.domain.excel.TblTerminalExcel;
import com.ruoyi.safe.service.ITblAssetOverviewService;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblTerminalService;
import com.ruoyi.safe.vo.countDict.CountByDictVO;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 终端/感知/现场设备Controller
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@RestController
@RequestMapping("/safe/terminal")
public class TblTerminalController extends BaseController
{
    @Autowired
    private ITblTerminalService tblTerminalService;
    @Resource
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;

    /**
     * 查询终端/感知/现场设备列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:terminal:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblTerminal tblTerminal)
    {
        PageUtils.startPage("asset_id desc");
        List<TblTerminal> list = tblTerminalService.selectTblTerminalList(tblTerminal);
        if (CollUtil.isNotEmpty( list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("3");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    TblTerminal terminal = list.get(i);
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        basicInformationList.forEach(basicInformationInfo ->{
                            String fieldKey = basicInformationInfo.getString("fieldKey");
                            if ("optId".equals(fieldKey)){
                                if (terminal.getOptsystem() != null){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            }else {
                                Object fieldValue = ReflectUtil.getFieldValue(terminal, fieldKey);
                                if (fieldValue != null){
                                    numerator.getAndSet(numerator.get() + 1);
                                }
                            }
                        });
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    double formattedCompletionRate = bd.doubleValue();
                    terminal.setCompleteness(formattedCompletionRate);
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 导出终端/感知/现场设备列表
     */
    @PreAuthorize("@ss.hasPermi('safe:terminal:export')")
    @Log(title = "终端/感知/现场设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblTerminal tblTerminal)
    {
        List<TblTerminal> list = tblTerminalService.selectTblTerminalList(tblTerminal);
        List<TblTerminalExcel> excelList = new ArrayList<>();
        if(CollUtil.isNotEmpty(list)){
            excelList = list.stream().map(item -> {
                TblTerminalExcel terminalExcel = new TblTerminalExcel();
                BeanUtil.copyProperties(item,terminalExcel);
                if(item.getOptsystem() != null){
                    terminalExcel.setProcName(item.getOptsystem().getProcName());
                }
                return terminalExcel;
            }).collect(Collectors.toList());
        }
        ExcelUtil<TblTerminalExcel> util = new ExcelUtil<TblTerminalExcel>(TblTerminalExcel.class);
        util.exportExcel(response, excelList, "终端数据");
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TblTerminalExcel> util = new ExcelUtil<TblTerminalExcel>(TblTerminalExcel.class);
        List<TblTerminalExcel> list = new ArrayList<>();
        TblTerminalExcel tblTerminalExcel = new TblTerminalExcel();
        tblTerminalExcel.setAssetCode("例如：ZD20250507150317");
        tblTerminalExcel.setAssetTypeDesc("例如：windows终端");
        tblTerminalExcel.setDeptName("例如：XX部门");
        tblTerminalExcel.setUserName("例如：使用人账号");
        tblTerminalExcel.setDomainName("例如：一级网络一级子网络");
        tblTerminalExcel.setIpv4("例如：***********");
        tblTerminalExcel.setMac("例如：01:04:05:06:05:50");
        tblTerminalExcel.setProcName("例如：Microsoft Windows 10");
        list.add(tblTerminalExcel);
        util.exportExcel(response,list,"终端模板");
    }

    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        return tblTerminalService.importData(file, updateSupport);
    }

    /**
     * 获取终端/感知/现场设备详细信息
     */
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblTerminalService.selectTblTerminalByAssetId(assetId));
    }

    /**
     * 新增终端/感知/现场设备
     */
    @PreAuthorize("@ss.hasPermi('safe:terminal:add')")
    @Log(title = "终端/感知/现场设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblTerminal tblTerminal)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            if (StringUtils.isNotEmpty(tblTerminal.getAssetCode()) && !tblAssetOverviewService.checkAssetCodeUnique(tblTerminal)) {
                throw new ServiceException("资产编码已存在");
            }
            tblTerminal.setCreateBy(loginUser.getUsername());
        }
        return toAjax(tblTerminalService.insertTblTerminal(tblTerminal));
    }

    /**
     * 修改终端/感知/现场设备
     */
    @PreAuthorize("@ss.hasPermi('safe:terminal:edit')")
    @Log(title = "终端/感知/现场设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblTerminal tblTerminal)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            if (StringUtils.isNotEmpty(tblTerminal.getAssetCode()) && !tblAssetOverviewService.checkAssetCodeUnique(tblTerminal)) {
                throw new ServiceException("资产编码已存在");
            }
            tblTerminal.setUpdateBy(loginUser.getUsername());
        }
        return toAjax(tblTerminalService.updateTblTerminal(tblTerminal));
    }

    /**
     * 删除终端/感知/现场设备
     */
    @PreAuthorize("@ss.hasPermi('safe:terminal:remove')")
    @Log(title = "终端/感知/现场设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblTerminalService.deleteTblTerminalByAssetIds(assetIds));
    }

    /**
     * 获取部门服务器统计
     */
    @GetMapping("/getDepts")
    public AjaxResult getDepts() {
        QueryDeptTerminalCountDto queryCountDto = new QueryDeptTerminalCountDto();
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        return AjaxResult.success(tblTerminalService.getDeptTerminalCount(queryCountDto));
    }
}
