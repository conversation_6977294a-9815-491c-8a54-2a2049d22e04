package jnpf.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.controller.SuperController;
import jnpf.base.vo.ListVO;
import jnpf.constant.MsgCode;
import jnpf.entity.ScheduleEntity;
import jnpf.exception.DataException;
import jnpf.model.schedule.*;
import jnpf.service.ScheduleService;
import jnpf.util.DateUtil;
import jnpf.util.JsonUtil;
import jnpf.util.JsonUtilEx;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日程安排
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @date 2019年9月26日 上午9:18
 */
@Tag(name = "日程安排", description = "Schedule")
@RestController
@RequestMapping("/api/extend/Schedule")
public class ScheduleController extends SuperController<ScheduleService, ScheduleEntity> {

    @Autowired
    private ScheduleService scheduleService;

    /**
     * 列表
     *
     * @param scheduleTime 分页模型
     * @return
     */
    @Operation(summary = "获取日程安排列表")
    @GetMapping
    @SaCheckPermission("extend.schedule")
    public ActionResult<ListVO<ScheduleListVO>> list(ScheduleTime scheduleTime) {
        List<ScheduleEntity> data = scheduleService.getList(scheduleTime.getStartTime(), scheduleTime.getEndTime());
        List<ScheduleListVO> list = JsonUtil.getJsonToList(data, ScheduleListVO.class);
        ListVO<ScheduleListVO> listVO = new ListVO();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }

    /**
     * 列表（app使用）
     *
     * @param scheduleTime 分页模型
     * @return
     */
    @Operation(summary = "列表")
    @GetMapping("/AppList")
    @SaCheckPermission("extend.schedule")
    public ActionResult appList(ScheduleTimes scheduleTime) {
        Map<String, Object> days = new LinkedHashMap<>();
        List<ScheduleEntity> scheduleList = scheduleService.getList(scheduleTime.getStartTime(), scheduleTime.getEndTime());
        Date start = DateUtil.stringToDates(scheduleTime.getStartTime());
        Date end = DateUtil.stringToDates(scheduleTime.getEndTime());
        List<Date> item = DateUtil.getAllDays(start, end);
        if(StringUtils.isEmpty(scheduleTime.getDateTime())){
            scheduleTime.setDateTime(DateUtil.dateNow("yyyyMMdd"));
        }else{
            scheduleTime.setDateTime(scheduleTime.getDateTime().replaceAll("-",""));
        }
        Map<String,List<ScheduleEntity>> dataList = new HashMap<>();
        for (int i = 0; i < item.size(); i++) {
            String startTime = DateUtil.daFormat(item.get(i)) + " 00:00";
            String endTime = DateUtil.daFormat(item.get(i)) + " 23:59";
            List<ScheduleEntity> count = scheduleList.stream().filter(m -> DateUtil.dateFormat(m.getStartTime()).compareTo(endTime) <= 0 && DateUtil.dateFormat(m.getEndTime()).compareTo(startTime) >= 0).collect(Collectors.toList());
            String time = DateUtil.daFormat(item.get(i)).replaceAll("-", "");
            days.put(time, count.size());
            dataList.put(time,count);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("signList", days);
        List<ScheduleEntity> todayList = dataList.get(scheduleTime.getDateTime());
        data.put("todayList", JsonUtil.listToJsonField(todayList));
        return ActionResult.success(data);
    }

    /**
     * 信息
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "获取日程安排信息")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.schedule")
    public ActionResult<ScheduleInfoVO> info(@PathVariable("id") String id) throws DataException {
        ScheduleEntity entity = scheduleService.getInfo(id);
        ScheduleInfoVO vo = JsonUtilEx.getJsonToBeanEx(entity, ScheduleInfoVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 新建
     *
     * @param scheduleCrForm 日程模型
     * @return
     */
    @Operation(summary = "新建日程安排")
    @PostMapping
    @Parameters({
            @Parameter(name = "scheduleCrForm", description = "日程模型",required = true),
    })
    @SaCheckPermission("extend.schedule")
    public ActionResult create(@RequestBody @Valid ScheduleCrForm scheduleCrForm) {
        ScheduleEntity entity = JsonUtil.getJsonToBean(scheduleCrForm, ScheduleEntity.class);
        scheduleService.create(entity);
        return ActionResult.success("新建成功");
    }

    /**
     * 更新
     *
     * @param id             主键值
     * @param scheduleUpForm 日程模型
     * @return
     */
    @Operation(summary = "更新日程安排")
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "scheduleUpForm", description = "日程模型",required = true),
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.schedule")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid ScheduleUpForm scheduleUpForm) {
        ScheduleEntity entity = JsonUtil.getJsonToBean(scheduleUpForm, ScheduleEntity.class);
        boolean flag = scheduleService.update(id, entity);
        if (flag == false) {
            return ActionResult.fail("更新失败，数据不存在");
        }
        return ActionResult.success("更新成功");
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "删除日程安排")
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键",required = true),
    })
    @SaCheckPermission("extend.schedule")
    public ActionResult delete(@PathVariable("id") String id) {
        ScheduleEntity entity = scheduleService.getInfo(id);
        if (entity != null) {
            scheduleService.delete(entity);
            return ActionResult.success("删除成功");
        }
        return ActionResult.fail("删除失败，数据不存在");
    }

}
