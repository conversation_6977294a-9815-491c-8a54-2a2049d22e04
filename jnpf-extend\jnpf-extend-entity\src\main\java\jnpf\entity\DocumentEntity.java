package jnpf.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 知识文档
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("ext_document")
public class DocumentEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    /**
     * 文档父级
     */
    @TableField("F_PARENTID")
    private String parentId;

    /**
     * 文档分类
     */
    @TableField("F_TYPE")
    private Integer type;

    /**
     * 文件名称
     */
    @TableField("F_FULLNAME")
    private String fullName;

    /**
     * 文件路径
     */
    @TableField("F_FILEPATH")
    private String filePath;

    /**
     * 文件大小
     */
    @TableField("F_FILESIZE")
    private String fileSize;

    /**
     * 文件后缀
     */
    @TableField("F_FILEEXTENSION")
    private String fileExtension;

    /**
     * 阅读数量
     */
    @TableField("F_READCCOUNT")
    private Integer readcCount;

    /**
     * 是否共享
     */
    @TableField("F_ISSHARE")
    private Integer isShare;

    /**
     * 共享时间
     */
    @TableField("F_SHARETIME")
    private Date shareTime;


    /**
     * 文档下载地址
     */
    @TableField("F_UPLOADERURL")
    private String uploaderUrl;


}
