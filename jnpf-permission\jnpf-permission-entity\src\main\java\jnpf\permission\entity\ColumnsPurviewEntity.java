package jnpf.permission.entity;

import jnpf.base.entity.SuperEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 模块列表权限
 *
 * <AUTHOR>
 * @version: V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date ：2022/3/15 9:20
 */
@Data
@TableName(value = "base_columnspurview")
public class ColumnsPurviewEntity extends SuperEntity<String> {

    /**
     * 列表字段数组
     */
    @TableField("F_FIELDLIST")
    private String fieldList;
    /**
     * 模块ID
     */
    @TableField("F_MODULEID")
    private String moduleId;

    /**
     * 排序码
     */
    @TableField("F_SORTCODE")
    private Long sortCode;

    /**
     * 有效标志
     */
    @TableField("F_ENABLEDMARK")
    private Integer enabledMark;

}
