package com.ruoyi.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.TblSafety;
import com.ruoyi.safe.domain.TblZoneBoundary;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblZoneBoundaryService;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 区域边界Controller
 *
 * <AUTHOR>
 * @date 2023-01-06
 */
@RestController
@RequestMapping("/safe/boundary")
public class TblZoneBoundaryController extends BaseController
{
    @Autowired
    private ITblZoneBoundaryService tblZoneBoundaryService;
    @Autowired
    private ICountByDictService countByDictService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;

    /**
     * 查询区域边界列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:boundary:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblZoneBoundary tblZoneBoundary)
    {
        startPage();
        List<TblZoneBoundary> list = tblZoneBoundaryService.selectTblZoneBoundaryList(tblZoneBoundary);
        if(CollUtil.isNotEmpty( list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("6");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    TblZoneBoundary zoneBoundary = list.get(i);
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        for (JSONObject jsonObject : basicInformationList){
                            String fieldKey = jsonObject.getString("fieldKey");
                            Object fieldValue = ReflectUtil.getFieldValue(zoneBoundary, fieldKey);
                            if (fieldValue != null && !"".equals(fieldValue)){
                                numerator.getAndSet(numerator.get() + 1);
                            }
                        }
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    double formattedCompletionRate = bd.doubleValue();
                    zoneBoundary.setCompleteness(formattedCompletionRate);
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 导出区域边界列表
     */
    @PreAuthorize("@ss.hasPermi('safe:boundary:export')")
    @Log(title = "区域边界", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblZoneBoundary tblZoneBoundary)
    {
        List<TblZoneBoundary> list = tblZoneBoundaryService.selectTblZoneBoundaryList(tblZoneBoundary);
        ExcelUtil<TblZoneBoundary> util = new ExcelUtil<TblZoneBoundary>(TblZoneBoundary.class);
        util.exportExcel(response, list, "区域边界数据");
    }

    /**
     * 获取区域边界详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:boundary:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblZoneBoundaryService.selectTblZoneBoundaryByAssetId(assetId));
    }

    /**
     * 新增区域边界
     */
    @PreAuthorize("@ss.hasPermi('safe:boundary:add')")
    @Log(title = "区域边界", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblZoneBoundary tblZoneBoundary)
    {
        AjaxResult ajaxResult = toAjax(tblZoneBoundaryService.insertTblZoneBoundary(tblZoneBoundary));
        ajaxResult.put("data",tblZoneBoundary);
        return ajaxResult;
    }

    /**
     * 修改区域边界
     */
    @PreAuthorize("@ss.hasPermi('safe:boundary:edit')")
    @Log(title = "区域边界", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblZoneBoundary tblZoneBoundary)
    {
        return toAjax(tblZoneBoundaryService.updateTblZoneBoundary(tblZoneBoundary));
    }

    /**
     * 删除区域边界
     */
    @PreAuthorize("@ss.hasPermi('safe:boundary:remove')")
    @Log(title = "区域边界", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblZoneBoundaryService.deleteTblZoneBoundaryByAssetIds(assetIds));
    }

    /**
     * 根据不同的字典类型统计区域边界数量
     * @param dictType
     * @return
     */
    @GetMapping("/getBoundaryCountByDict")
    public AjaxResult getBoundaryCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"boundary");
        return AjaxResult.success(countByDict);
    }
}
