package com.ruoyi.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.dict.service.ITblLocationService;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.TblAssetOverview;
import com.ruoyi.safe.domain.TblNetworkDevices;
import com.ruoyi.safe.domain.TblNetworkDevicesTemlate;
import com.ruoyi.safe.domain.TblVendor;
import com.ruoyi.safe.mapper.TblVendorMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.safe.domain.dto.QueryDeptNetworkDevicesCountDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 网络设备Controller
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RestController
@RequestMapping("/safe/networkdevices")
public class TblNetworkDevicesController extends BaseController
{
    @Autowired
    private ITblNetworkDevicesService tblNetworkDevicesService;

    @Autowired
    private ICountByDictService countByDictService;
    @Autowired
    private IMonitorHandleService monitorHandleService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ITblVendorService tblVendorService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private IAssetVulnerabilityStatsService assetVulnerabilityStatsService;
    @Autowired
    private ITblLocationService tblLocationService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;

    private static final String IPV4_REGEX =
            "^(?:(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])\\.){3}(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])$";
    /**
     * 查询网络设备列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:networkdevices:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblNetworkDevices tblNetworkDevices)
    {
        startPage();
        List<TblNetworkDevices> list = tblNetworkDevicesService.selectTblNetworkDevicesList(tblNetworkDevices);
        if (CollUtil.isNotEmpty( list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("4");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i <list.size(); i++) {
                    TblNetworkDevices domain = list.get(i);
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        for (JSONObject jsonObject : basicInformationList){
                            String fieldKey = jsonObject.getString("fieldKey");
                            Object fieldValue = ReflectUtil.getFieldValue(domain, fieldKey);
                            if (fieldValue != null && !"".equals(fieldValue)){
                                numerator.getAndSet(numerator.get() + 1);
                            }
                        }
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    double formattedCompletionRate = bd.doubleValue();
                    domain.setCompleteness(formattedCompletionRate);
                }
            }
        }

        // 为网络设备列表添加统计信息
        assetVulnerabilityStatsService.batchEnrichNetworkDevicesWithStats(list);

        return getDataTable(list);
    }

    /**
     * 导出网络设备列表
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:export')")
    @Log(title = "网络设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblNetworkDevices tblNetworkDevices)
    {
        List<TblNetworkDevices> list = tblNetworkDevicesService.selectTblNetworkDevicesList(tblNetworkDevices);
        list.forEach(e -> {
            if (StringUtils.isNotBlank(e.getLaseScanState())) {
                e.setState(e.getLaseScanState());
            }
        });
        ExcelUtil<TblNetworkDevices> util = new ExcelUtil<TblNetworkDevices>(TblNetworkDevices.class);
        util.exportExcel(response, list, "网络设备数据");
    }

    /**
     * 获取网络设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblNetworkDevicesService.selectTblNetworkDevicesByAssetId(assetId));
    }

    /**
     * 新增网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:add')")
    @Log(title = "网络设备", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody TblNetworkDevices tblNetworkDevices)
    {
        if (StrUtil.isNotBlank(tblNetworkDevices.getIp())){
            List<String> ips = StrUtil.split(tblNetworkDevices.getIp(),",");
            monitorHandleService.deleteByIp(ips);
        }
        AjaxResult ajaxResult = toAjax(tblNetworkDevicesService.insertTblNetworkDevices(tblNetworkDevices));
        ajaxResult.put("data",tblNetworkDevices);
        return ajaxResult;
    }

    /**
     * 修改网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:edit')")
    @Log(title = "网络设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblNetworkDevices tblNetworkDevices)
    {
        return toAjax(tblNetworkDevicesService.updateTblNetworkDevices(tblNetworkDevices));
    }

    /**
     * 删除网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:remove')")
    @Log(title = "网络设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblNetworkDevicesService.deleteTblNetworkDevicesByAssetIds(assetIds));
    }

    /**
     * 根据不同的字典类型统计网络设备数量
     * @param dictType
     * @return
     */
    @GetMapping("/getNetworkdevicesCountByDict")
    public AjaxResult getNetworkdevicesCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"networkdevices");
        return AjaxResult.success(countByDict);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TblNetworkDevicesTemlate> util = new ExcelUtil<TblNetworkDevicesTemlate>(TblNetworkDevicesTemlate.class);
        List<TblNetworkDevicesTemlate> list = new ArrayList<>();
        TblNetworkDevicesTemlate tblNetworkDevices = new TblNetworkDevicesTemlate();
        tblNetworkDevices.setAssetName("例如：XX网络设备");
        tblNetworkDevices.setIp("例如：***********");
        tblNetworkDevices.setAssetCode("例如：WLSB112323");
        tblNetworkDevices.setDeptName("例如：XX部门");
        tblNetworkDevices.setDomainName("例如：XX网络区域");
        tblNetworkDevices.setDegreeImportance("例如：非常重要/重要/一般/不太重要/不重要");
        tblNetworkDevices.setIsVirtual("例如：是/否");
        tblNetworkDevices.setSystemVersion("例如：Windows10");
        tblNetworkDevices.setBrandModel("例如：XX厂家");
        tblNetworkDevices.setVendorName("例如：XX供应商");
        tblNetworkDevices.setVer("例如：XX型号");
        tblNetworkDevices.setLocationDetail("例如：江西南昌XXX");
        /*tblNetworkDevices.setLocationFullName("XX机房");*/
        tblNetworkDevices.setPurpose("例如：XX用途");
        tblNetworkDevices.setRemark("例如：XX备注");
        list.add(tblNetworkDevices);
        util.exportExcel(response,list,"网络设备模板");
    }

    @PostMapping("/importData")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("文件不能为空");
        }
        if (file.getSize() > 10 * 1024 * 1024) { // 限制文件大小为10MB
            return AjaxResult.error("文件大小不能超过10MB");
        }

        ExcelUtil<TblNetworkDevicesTemlate> util = new ExcelUtil<>(TblNetworkDevicesTemlate.class);
        List<TblNetworkDevicesTemlate> list = util.importExcel(file.getInputStream());
        if (CollUtil.isEmpty(list)) {
            return AjaxResult.error("导入数据为空");
        }

        try {
            // 缓存部门、字典和网络区域信息
            Map<String, SysDept> deptMap = deptService.selectDeptList(new SysDept())
                    .stream().collect(Collectors.toMap(SysDept::getDeptName, dept -> dept));
            SysDictData dictData = new SysDictData();
            dictData.setDictType("impt_grade");
            Map<String, SysDictData> dictMap = dictDataService.selectDictDataList(dictData)
                    .stream().collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
            Map<String, NetworkDomain> domainMap = networkDomainService.selectNetworkDomainList(new NetworkDomain())
                    .stream().collect(Collectors.toMap(NetworkDomain::getDomainName, domain -> domain));
            Map<String, TblVendor> vendorMap = tblVendorService.selectTblVendorList(new TblVendor())
                    .stream().collect(Collectors.toMap(TblVendor::getVendorName, vendor -> vendor));
            Map<String, TblLocation> vendorLocationMap = tblLocationService.selectTblLocationList(new TblLocation())
                    .stream().collect(Collectors.toMap(TblLocation::getLocationFullName, location -> location));

            int index = 1; // 行号从1开始
            for (TblNetworkDevicesTemlate tblNetworkDevices : list) {
                // 校验字段
                checkField(tblNetworkDevices.getAssetName(), "资产名称", index);
                checkField(tblNetworkDevices.getIp(), "IP地址", index);
                checkField(tblNetworkDevices.getDeptName(), "所属部门", index);
                checkField(tblNetworkDevices.getDomainName(), "所属网络", index);

                // 校验IP地址
                if (!isValidIPv4(tblNetworkDevices.getIp())) {
                    throw new ServiceException("第" + index + "行, IP地址格式错误");
                }

                // 自动生成资产编码
                if (StrUtil.isBlank(tblNetworkDevices.getAssetCode())){
                    tblNetworkDevices.setAssetCode("WLSB"+DateUtil.format(new Date(), "yyyyMMddHHmmss"+index));
                }

                // 校验供应商
                TblVendor tblVendor = vendorMap.get(tblNetworkDevices.getVendorName());
                if (tblVendor == null) {
                    /*throw new ServiceException("第" + index + "行, 供应商不存在");*/
                    tblNetworkDevices.setVendor(null);
                }else {
                    tblNetworkDevices.setVendor(tblVendor.getId());
                }

                //校验资产位置
                TblLocation tblLocation = vendorLocationMap.get(tblNetworkDevices.getLocationFullName());
                if (tblLocation == null) {
                    /*throw new ServiceException("第" + index + "行, 位置不存在");*/
                    tblNetworkDevices.setLocationId(null);
                }else{
                    tblNetworkDevices.setLocationId(tblLocation.getLocationId().toString());
                }

                //校验虚拟设备
                if (StrUtil.isNotBlank(tblNetworkDevices.getIsVirtual())){
                    if ("是".equals(tblNetworkDevices.getIsVirtual())){
                        tblNetworkDevices.setIsVirtual("Y");
                    }else if ("否".equals(tblNetworkDevices.getIsVirtual())){
                        tblNetworkDevices.setIsVirtual("N");
                    }
                }

                // 校验部门
                SysDept dept = deptMap.get(tblNetworkDevices.getDeptName());
                if (dept == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDeptName() + " 部门不存在");
                }
                tblNetworkDevices.setDeptId(dept.getDeptId());

                // 校验网络区域
                NetworkDomain domain = domainMap.get(tblNetworkDevices.getDomainName());
                if (domain == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDomainName() + " 网络区域不存在");
                }
                tblNetworkDevices.setDomainId(domain.getDomainId());

                // 校验重要程度
                if (StrUtil.isNotBlank(tblNetworkDevices.getDegreeImportance())) {
                    SysDictData dictDataFind = dictMap.get(tblNetworkDevices.getDegreeImportance());
                    if (dictDataFind == null) {
                        throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDegreeImportance() + " 重要程度不存在");
                    }
                    tblNetworkDevices.setDegreeImportance(dictDataFind.getDictValue());
                }
                //校验资产编码是否唯一
                TblAssetOverview tblAssetOverview = new TblAssetOverview();
                tblAssetOverview.setAssetCode(tblNetworkDevices.getAssetCode());
                if (com.ruoyi.common.utils.StringUtils.isNotEmpty(tblNetworkDevices.getAssetCode()) && !tblAssetOverviewService.checkAssetCodeUnique(tblAssetOverview)) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getAssetCode() + "资产编码已存在");
                }

                // 插入数据
                TblNetworkDevices insertTblNetworkDevices = new TblNetworkDevices();
                BeanUtils.copyProperties(tblNetworkDevices, insertTblNetworkDevices);
                insertTblNetworkDevices.setAssetId(snowflake.nextId());
                insertTblNetworkDevices.setCreateBy(getUsername());
                if (insertTblNetworkDevices.getUserId() == null) {
                    insertTblNetworkDevices.setUserId(getUserId());
                }
                if (insertTblNetworkDevices.getDeptId() == null) {
                    insertTblNetworkDevices.setDeptId(getDeptId());
                }
                if (tblNetworkDevicesService.insertTblNetworkDevices(insertTblNetworkDevices) <= 0) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getAssetName() + " 网络设备导入失败");
                }

                index++;
            }
        } catch (ServiceException e) {
            logger.error("导入数据出错: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("系统异常: {}", e.getMessage(), e);
            throw new ServiceException("数据导入错误: " + e.getMessage());
        }

        return AjaxResult.success("导入成功");
    }

    private void checkField(String value, String fieldName,int index) throws Exception {
        if (StrUtil.isBlank(value)) {
            throw new Exception("导入失败:第"+index+"行,"+ fieldName + "不能为空");
        }
    }

    public static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        return ip.matches(IPV4_REGEX);
    }

    /**
     * 获取部门网络设备统计
     */
    @GetMapping("/getDepts")
    public AjaxResult getDepts() {
        QueryDeptNetworkDevicesCountDto queryCountDto = new QueryDeptNetworkDevicesCountDto();
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        return AjaxResult.success(tblNetworkDevicesService.getDeptNetworkDevicesCount(queryCountDto));
    }

}
