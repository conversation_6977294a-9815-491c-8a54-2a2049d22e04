package jnpf.permission.mapper;

import jnpf.base.mapper.SuperMapper;
import jnpf.model.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * RuoYi用户信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Mapper
public interface RuoYiUserMapper extends SuperMapper<SysUser> {

    List<SysUser> listSysUserByRole(@Param("roleIds") List<Long> roleIds);
}
