package jnpf.message.entity;

import jnpf.base.entity.SuperEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 消息中心模块数据类型表
 *
 * @版本： V3.2.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2022-08-18
 */
@Data
@TableName("base_message_data_type")
public class MessageDataTypeEntity extends SuperEntity<String> {

    @TableField("F_TYPE")
    private String type;

    @TableField("F_FULLNAME")
    private String fullName;

    @TableField("F_ENCODE")
    private String enCode;

    /**
     * 状态
     */
    @TableField("F_ENABLEDMARK")
    private Integer enabledMark;
}
