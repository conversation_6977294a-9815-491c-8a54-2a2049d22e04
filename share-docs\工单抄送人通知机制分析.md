# 工单抄送人通知机制分析报告

## 分析目标

分析 aqsoc-main 项目下 aqsoc-monitor 模块调用威胁通报列表接口 `https://**************/prod-api/work/order/waitList?pageNum=1&pageSize=10` 的工单列表，在新增时设置了抄送人是否会生成 SysNotice 通知。

## 项目架构概述

### 1. 项目关系
- **aqsoc-main**：安全运营平台主项目
- **aq-jnpf**：工作流引擎项目，处理业务流程
- **通信方式**：aqsoc-main 通过 smiley-http-proxy-servlet 代理调用 aq-jnpf 项目接口

### 2. 代理配置

**文件位置**：`aqsoc-admin/src/main/java/com/ruoyi/web/core/config/ProxyServletConfiguration.java`

```java
@Configuration
public class ProxyServletConfiguration {
    @Value("${jnpf.proxy.servlet_url}")
    private String servlet_url; // /proxy/*
    
    @Value("${jnpf.proxy.target_url}")
    private String target_url; // http://***************:30001
    
    @Bean
    public ServletRegistrationBean proxyServletRegistration() {
        ServletRegistrationBean registrationBean = new ServletRegistrationBean(createProxyServlet(), servlet_url);
        Map<String, String> params = MapUtil.builder(new HashMap<String, String>())
                .put(ProxyServlet.P_TARGET_URI, target_url)
                .put(ProxyServlet.P_HANDLEREDIRECTS, "false")
                .put(ProxyServlet.P_PRESERVECOOKIES, "true")
                .put(ProxyServlet.P_PRESERVEHOST, "true")
                .put(ProxyServlet.P_LOG, "true").build();
        registrationBean.setInitParameters(params);
        return registrationBean;
    }
}
```

**配置说明**：
- 代理路径：`/proxy/*` → `http://***************:30001`
- 示例调用：`https://**************/prod-api/proxy/api/workflow/Engine/FlowTask`

## 工单接口分析

### 1. 待办列表接口

**文件位置**：`aqsoc-monitor/src/main/java/com/ruoyi/work/controller/TblWorkOrderController.java`

```java
@GetMapping("/waitList")
public TableDataInfo list(TblWorkOrder tblWorkOrder) {
    startPage();
    List<TblWorkOrder> list = tblWorkOrderService.selectWaitList(tblWorkOrder);
    return getDataTable(list);
}
```

### 2. 工单服务实现

**文件位置**：`aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java`

```java
@Override
public List<TblWorkOrder> selectWaitList(TblWorkOrder tblWorkOrder) {
    handlePrem(tblWorkOrder);
    List<TblWorkOrder> tblWorkOrders = tblWorkOrderMapper.selectWaitList(tblWorkOrder);
    // 处理附件和历史节点属性
    return tblWorkOrders;
}
```

## SysNotice 通知机制分析

### 1. SysNotice 实体结构

**文件位置**：`aqsoc-system/src/main/java/com/ruoyi/system/domain/SysNotice.java`

```java
public class SysNotice extends BaseEntity {
    /** 公告ID */
    private Long noticeId;
    
    /** 公告标题 */
    private String noticeTitle;
    
    /** 公告类型（1通知公告 2待办通告） */
    private String noticeType;
    
    /** 接收对象类型（all/role/dept/user） */
    private String acceptType;
    
    /** 接收对象ID列表（JSON格式） */
    private String acceptIds;
    
    /** 业务ID */
    private Long businessId;
}
```

### 2. SysNotice 生成逻辑

**文件位置**：`aqsoc-monitor/src/main/java/com/ruoyi/work/service/impl/TblWorkOrderServiceImpl.java`

```java
private void sendFlowNotify(JSONObject flowInfo, List<TblWorkBacklog> notifyList, TblWorkOrder tblWorkOrder) {
    LoginUser loginUser = SecurityUtils.getLoginUser();
    ThreadUtil.execute(() -> {
        JSONObject flowNotify = flowInfo.getJSONObject("flowNotify");
        if (flowNotify == null || flowNotify.getBoolean("enabled") == null) {
            return;
        }
        
        // 流程通知 - 只为待办人员生成 SysNotice
        if (CollUtil.isNotEmpty(notifyList) && CollUtil.isNotEmpty(flowNotify.getJSONArray("types"))) {
            List<Long> types = flowNotify.getJSONArray("types").stream()
                .map(typeItem -> Long.parseLong(typeItem.toString()))
                .collect(Collectors.toList());
            
            TblWorkOrder tblWorkOrderInDB = tblWorkOrderMapper.selectTblWorkOrderById(tblWorkOrder.getId());
            
            types.forEach(type -> {
                if (type == FlowNotifyType.INTERIOR_MESSAGE.getCode()) {
                    // 站内信 - 生成 SysNotice
                    notifyList.forEach(backlog -> {
                        SysNotice sysNotice = new SysNotice();
                        sysNotice.setAcceptType("user");
                        sysNotice.setAcceptIds("[" + backlog.getHandleUser() + "]");
                        sysNotice.setNoticeTitle("您有待处理工单(" + tblWorkOrderInDB.getWorkName() + "),请及时处理");
                        sysNotice.setNoticeType("2"); // 待办通告
                        sysNotice.setCreateTime(DateUtil.date());
                        sysNotice.setCreateBy(loginUser.getUsername());
                        sysNotice.setBusinessId(tblWorkOrderInDB.getId());
                        sysNotice.setStatus("0");
                        sysNoticeService.insertNotice(sysNotice);
                        log.info("工单发送站内信通知:{},{}", backlog.getHandleUser(), sysNotice.getNoticeTitle());
                    });
                }
            });
        }
        
        // 抄送通知 - 不生成 SysNotice，使用短信等其他方式
        Boolean isReject = flowInfo.getBoolean("isReject");
        if (isReject == null || !isReject) {
            String smsTemplate = flowInfo.getString("copySmsTemplate");
            Integer smsTemplateType = flowInfo.getInteger("copySmsTemplateType");
            String copyIds = flowInfo.getString("copyIds");
            if (StrUtil.isNotBlank(smsTemplate) && StrUtil.isNotBlank(copyIds)) {
                List<Long> copyUserIds = StrUtil.split(copyIds, ",").stream()
                    .map(Long::parseLong).collect(Collectors.toList());
                List<SysUser> userList = userService.selectUserByIds(CollUtil.distinct(copyUserIds));
                if (CollUtil.isNotEmpty(userList)) {
                    // 发送短信通知，不是 SysNotice
                    JSONObject smsData = new JSONObject();
                    smsData.put("userList", userList);
                    smsData.put("smsTemplate", smsTemplate);
                    smsData.put("smsTemplateType", smsTemplateType);
                }
            }
        }
    });
}
```

**关键发现**：
- SysNotice 只在 `sendFlowNotify` 方法中为 `notifyList`（待办人员）生成
- 抄送人通知使用短信等其他方式，不生成 SysNotice

## 抄送人处理机制分析

### 1. JNPF 工作流中的抄送处理

**文件位置**：`jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/service/impl/FlowTaskNewServiceImpl.java`

```java
// 获取抄送人
List<FlowTaskCirculateEntity> circulateList = new ArrayList<>();
flowTaskUtil.circulateList(nodeModel, circulateList, flowModel, flowTask);
flowTaskCirculateService.create(circulateList);

// 发送消息
FlowMsgModel flowMsgModel = new FlowMsgModel();
flowMsgModel.setApprove(FlowNature.AuditCompletion.equals(taskNode.getCompletion()));
flowMsgModel.setCopy(true);
flowMsgModel.setNodeList(taskNodeList);
flowMsgModel.setCirculateList(circulateList);
```

### 2. 抄送人列表生成

**文件位置**：`jnpf-workflow/jnpf-workflow-engine/jnpf-workflow-engine-biz/src/main/java/jnpf/engine/util/FlowTaskUtil.java`

```java
public void circulateList(ChildNodeList nodeModel, List<FlowTaskCirculateEntity> circulateList, 
                         FlowModel flowModel, FlowTaskEntity flowTask) {
    Properties properties = nodeModel.getProperties();
    List<String> userIdAll = new ArrayList<>();
    userIdAll.addAll(properties.getCirculateUser());
    
    // 处理角色、职位、组织等抄送对象
    List<String> userAll = new ArrayList<>();
    userAll.addAll(properties.getCirculateRole());
    userAll.addAll(properties.getCirculatePosition());
    userAll.addAll(properties.getCirculateGroup());
    userAll.addAll(properties.getCirculateOrg());
    
    // 生成抄送实体列表
    for (String userId : userIdAll) {
        FlowTaskCirculateEntity entity = new FlowTaskCirculateEntity();
        entity.setId(RandomUtil.uuId());
        entity.setTaskId(flowTask.getId());
        entity.setCirculateUserId(userId);
        entity.setCreatorTime(new Date());
        circulateList.add(entity);
    }
}
```

### 3. JNPF 消息发送机制

**文件位置**：`jnpf-system/jnpf-system-biz/src/main/java/jnpf/base/util/SentMessageUtil.java`

```java
public void sendMessage(SentMessageForm sentMessageForm) {
    List<String> toUserIdsList = sentMessageForm.getToUserIds();
    String templateId = sentMessageForm.getTemplateId();
    
    // 根据发送类型处理
    switch (sendType) {
        case "1":
            // 站内消息 - 存储到 base_message 表
            messageService.sendScheduleMessage(sentMessageForm);
            break;
        case "2":
            // 邮件
            SendMail(toUserIdsList, userInfo, sendType, entity1, parameterMap, contentMsg);
            break;
        case "3":
            // 发送短信
            sendSms(toUserIdsList, userInfo, entity1, parameterMap, contentMsg);
            break;
        case "4":
            // 钉钉
        case "5":
            // 企业微信
        default:
            break;
    }
}
```

### 4. JNPF 站内消息实现

**文件位置**：`jnpf-message/jnpf-message-biz/src/main/java/jnpf/message/service/impl/MessageServiceImpl.java`

```java
@Override
public void sendScheduleMessage(SentMessageForm sentMessageForm) {
    List<String> toUserIds = sentMessageForm.getToUserIds();
    String title = sentMessageForm.getTitle();
    String bodyText = JsonUtil.getObjectToString(sentMessageForm.getContentMsg());
    
    MessageEntity entity = new MessageEntity();
    entity.setTitle(title);
    entity.setBodyText(bodyText);
    entity.setType(4); // 流程消息类型
    entity.setId(RandomUtil.uuId());
    entity.setFlowType(1);
    entity.setCreatorUser(userInfo.getUserId());
    entity.setCreatorTime(new Date());
    entity.setEnabledMark(1);
    
    // 存储到 base_message 表，不是 sys_notice 表
    this.save(entity);
    
    // 创建接收人记录
    List<MessageReceiveEntity> receiveEntityList = new ArrayList<>();
    for (String userId : toUserIds) {
        MessageReceiveEntity messageReceiveEntity = new MessageReceiveEntity();
        messageReceiveEntity.setId(RandomUtil.uuId());
        messageReceiveEntity.setMessageId(entity.getId());
        messageReceiveEntity.setUserId(userId);
        messageReceiveEntity.setIsRead(0);
        receiveEntityList.add(messageReceiveEntity);
    }
    messageReceiveService.saveBatch(receiveEntityList);
}
```

## 消息系统对比

### 1. aqsoc-main 的 SysNotice 系统

| 特性 | 说明 |
|------|------|
| 存储表 | `sys_notice` |
| 用途 | 工单待办通知、系统公告 |
| 通知类型 | `noticeType: "2"` 表示待办通告 |
| 接收对象 | `acceptType: "user"`, `acceptIds: "[userId]"` |
| 业务关联 | `businessId` 关联具体工单 |

### 2. aq-jnpf 的 MessageEntity 系统

| 特性 | 说明 |
|------|------|
| 存储表 | `base_message` + `base_message_receive` |
| 用途 | 流程消息、抄送通知 |
| 消息类型 | `type: 4` 表示流程消息 |
| 接收对象 | 通过 `MessageReceiveEntity` 关联用户 |
| 业务关联 | 通过流程任务ID关联 |

## 分析结论

### 核心发现

**工单新增时设置抄送人不会生成 SysNotice 通知**

### 详细说明

1. **SysNotice 生成条件**：
   - 只有在工单流程通知中为**待办人员**（`TblWorkBacklog`）生成
   - 通知类型为站内信（`FlowNotifyType.INTERIOR_MESSAGE`）时才生成
   - 不为抄送人生成 SysNotice

2. **抄送人通知机制**：
   - 抄送人接收的是 JNPF 系统的 `MessageEntity`（存储在 `base_message` 表）
   - 通过 `FlowTaskCirculateEntity` 管理抄送关系
   - 支持多种通知方式：站内信、短信、邮件、企业微信、钉钉等

3. **两个系统的通知分离**：
   - **aqsoc-main**：使用 `SysNotice` 系统进行工单待办通知
   - **aq-jnpf**：使用 `MessageEntity` 系统进行流程消息通知（包括抄送）

4. **技术架构**：
   - aqsoc-main 通过 smiley-http-proxy-servlet 代理调用 aq-jnpf 接口
   - 两个项目使用不同的数据库表和通知机制
   - 消息系统相互独立，各自处理不同类型的通知

### 实际影响

- 抄送人不会在 aqsoc-main 的通知公告模块中看到 SysNotice 类型的通知
- 抄送人会在 aq-jnpf 的消息中心看到流程相关的消息通知
- 两个系统的通知入口和展示方式不同

## 建议

如果需要让抄送人也能在 aqsoc-main 系统中看到通知，需要：

1. 修改 `sendFlowNotify` 方法，增加对抄送人的 SysNotice 生成逻辑
2. 或者在前端统一展示两个系统的消息通知
3. 或者建立消息同步机制，将 JNPF 的抄送消息同步到 aqsoc-main 的 SysNotice 系统

---

**分析完成时间**：2025-01-30  
**分析人员**：Claude 4.0 sonnet  
**文档版本**：v1.0
