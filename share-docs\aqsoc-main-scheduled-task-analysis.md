# aqsoc-main 项目定时任务执行逻辑分析报告

## 📋 概述

本报告深入分析了 aqsoc-main 项目中定时任务的执行逻辑，重点解答了"立即执行"模式的实际执行频率和扫描进度同步机制。

## 🎯 问题一：定时任务"立即执行"模式分析

### 核心发现

当 SysJob 的执行周期设置为"立即执行"时：

```java
/** 执行周期，0立即执行，1每天，2每周，3每月 */
private Integer period;
```

**实际执行频率**: **执行一次后自动停止**

### 实现机制

#### 1. 立即执行判断逻辑

位置：`aqsoc-quartz/src/main/java/com/ruoyi/quartz/util/AbstractQuartzJob.java:117`

```java
// 判断任务是否为立即执行
if (Objects.equals(sysJob.getPeriod(), 0) && sysJob.getCronExpression().equals("* * * * * ?")) {
    sysJob.setStatus(ScheduleConstants.Status.PAUSE.getValue());
    sysJob.setCurrentStatus(2);
    try {
        SpringUtils.getBean(ISysJobService.class).changeStatus(sysJob);
    } catch (SchedulerException e1) {
        log.error(e1.getMessage());
        e1.printStackTrace();
    }
}
```

#### 2. 任务调度创建

位置：`aqsoc-quartz/src/main/java/com/ruoyi/quartz/util/ScheduleUtils.java:53`

```java
public static void createScheduleJob(Scheduler scheduler, SysJob job) throws SchedulerException, TaskException {
    Class<? extends Job> jobClass = getQuartzJobClass(job);
    // 构建job信息
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();

    // 表达式调度构建器
    CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
    cronScheduleBuilder = handleCronScheduleMisfirePolicy(job, cronScheduleBuilder);

    // 按新的cronExpression表达式构建一个新的trigger
    CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup))
            .withSchedule(cronScheduleBuilder).build();

    // 放入参数，运行时的方法可以获取
    jobDetail.getJobDataMap().put(ScheduleConstants.TASK_PROPERTIES, job);

    // 判断是否存在
    if (scheduler.checkExists(getJobKey(jobId, jobGroup))) {
        // 防止创建时存在数据问题 先移除，然后在执行创建操作
        scheduler.deleteJob(getJobKey(jobId, jobGroup));
    }

    // 判断任务是否过期
    if (StringUtils.isNotNull(CronUtils.getNextExecution(job.getCronExpression()))) {
        // 执行调度任务
        scheduler.scheduleJob(jobDetail, trigger);
    }

    // 暂停任务
    if (job.getStatus().equals(ScheduleConstants.Status.PAUSE.getValue())) {
        scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
    }
}
```

#### 3. 立即运行方法

位置：`aqsoc-quartz/src/main/java/com/ruoyi/quartz/service/impl/SysJobServiceImpl.java:194`

```java
/**
 * 立即运行任务
 *
 * @param job 调度信息
 */
@Override
@Transactional(rollbackFor = Exception.class)
public boolean run(SysJob job) throws SchedulerException {
    boolean result = false;
    Long jobId = job.getJobId();
    String jobGroup = job.getJobGroup();
    SysJob properties = selectJobById(job.getJobId());
    // 参数
    JobDataMap dataMap = new JobDataMap();
    dataMap.put(ScheduleConstants.TASK_PROPERTIES, properties);
    JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
    if (scheduler.checkExists(jobKey)) {
        result = true;
        scheduler.triggerJob(jobKey, dataMap);
    }
    return result;
}
```

### 接口查询逻辑

#### API 接口
- **路径**: `/monitor/schedule/list?pageNum=1&pageSize=10&jobType=1&jobGroup=ASSET_SCAN`
- **Controller**: `aqsoc-monitor/src/main/java/com/ruoyi/safe/controller/MonitorJobController.java:71`

#### 数据库查询

位置：`aqsoc-quartz/src/main/resources/mapper/quartz/SysJobMapper.xml:53`

```sql
<select id="selectJobList" parameterType="com.ruoyi.quartz.domain.SysJob" resultMap="SysJobResult">
    <include refid="selectJobVo"/>
    <where>
        t1.is_del = 0
        <if test="jobName != null and jobName != ''">
            AND t1.job_name like concat('%', #{jobName}, '%')
        </if>
        <if test="jobGroup != null and jobGroup != ''">
            AND t1.job_group = #{jobGroup}
        </if>
        <if test="status != null and status != ''">
            AND t1.`status` = #{status}
        </if>
        <if test="currentStatus != null">
            AND t1.current_status = #{currentStatus}
        </if>
        <if test="jobType != null">
            AND t1.job_type = #{jobType}
        </if>
    </where>
    order by t1.create_time desc
</select>
```

### SysJob 实体字段说明

```java
public class SysJob extends BaseEntity {
    /** 任务类型，0,探活，1，漏洞扫描，2，Web扫描 */
    private Integer jobType;
    
    /** 任务组名 */
    private String jobGroup;
    
    /** cron执行表达式 */
    private String cronExpression;
    
    /** 任务状态（0正常 1暂停） */
    private String status;
    
    /** 最近扫描状态，0未扫描，1扫描中，2扫描完成 */
    private Integer currentStatus;
    
    /** 执行周期，0立即执行，1每天，2每周，3每月 */
    private Integer period;
}
```

## 🎯 问题二：扫描进度同步频率分析

### 核心发现

接口 `/hostscan/tasksummary/list?taskType=2&jobId=406` 的扫描进度同步频率为：**每5秒同步一次**

### 实现机制

#### 1. 后台监控线程

位置：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/event/ScanResultMonitorEvent.java:389`

```java
private void startEvent() {
    Thread thread = new Thread(new Runnable() {
        @Override
        public void run() {
            while (bRun) {
                try {
                    Thread.sleep(5000);  // 5秒间隔
                    List<Integer> taskList = cloneTaskList();
                    for (int taskId: taskList) {
                        HostScanTaskSummaryResult hostScanTaskSummaryResult = getHostScanTaskSummary(taskId);
                        if (hostScanTaskSummaryResult != null) {
                            int nRet = dealFfsafeScanTaskSummary(taskId, hostScanTaskSummaryResult);
                            if (nRet == 1) {
                                // 处理成功，移除任务并重置失败计数
                                removeHostScanTask(taskId);
                                log.info("主机扫描任务 {} 处理完成，已移除", taskId);
                            } else if (nRet == 0) {
                                // 处理失败，增加失败计数
                                int failCount = incrementHostScanTaskFailCount(taskId);
                                log.warn("处理非凡主机扫描任务结果失败！taskId: {}, 失败次数: {}", taskId, failCount);
                            }
                        }
                    }
                } catch (InterruptedException e) {
                    log.error("扫描结果监控线程被中断", e);
                    break;
                }
            }
        }
    });
    thread.start();
}
```

#### 2. 接口控制器

位置：`aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/controller/FfsafeScantaskSummaryController.java:22`

```java
@RestController
@RequestMapping("/hostscan/tasksummary")
public class FfsafeScantaskSummaryController extends BaseController {
    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;

    /**
     * 查询非凡扫描任务汇总列表
     */
    @GetMapping("/list")
    public TableDataInfo list(FfsafeScantaskSummary ffsafeScantaskSummary) {
        startPage();
        List<FfsafeScantaskSummary> list = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        return getDataTable(list);
    }
}
```

#### 3. 数据库查询逻辑

位置：`aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScantaskSummaryMapper.xml:33`

```sql
<select id="selectFfsafeScantaskSummaryList" parameterType="FfsafeScantaskSummary" resultMap="FfsafeScantaskSummaryResult">
    <include refid="selectFfsafeScantaskSummaryVo"/>
    <where>
        <if test="jobId != null "> and job_id = #{jobId}</if>
        <if test="taskId != null "> and task_id = #{taskId}</if>
        <if test="taskType != null "> and task_type = #{taskType}</if>
        <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
        <if test="finishRate != null "> and finish_rate = #{finishRate}</if>
    </where>
</select>
```

#### 4. 进度更新机制

位置：`aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScantaskSummaryMapper.xml:139`

```sql
<update id="updateFfsafeScantaskSummaryByTaskId" parameterType="FfsafeScantaskSummary">
    update ffsafe_scantask_summary
    <trim prefix="SET" suffixOverrides=",">
        <if test="taskStatus != null">task_status = #{taskStatus},</if>
        <if test="finishRate != null">finish_rate = #{finishRate},</if>
        <if test="highRiskNum != null">high_risk_num = #{highRiskNum},</if>
        <if test="middleRiskNum != null">middle_risk_num = #{middleRiskNum},</if>
        <if test="lowRiskNum != null">low_risk_num = #{lowRiskNum},</if>
        <if test="endTime != null">end_time = #{endTime},</if>
    </trim>
    where task_id = #{taskId} and task_type = #{taskType}
</update>
```

### FfsafeScantaskSummary 实体字段说明

```java
public class FfsafeScantaskSummary extends BaseEntity {
    /** 定时任务id */
    private Integer jobId;
    
    /** 非凡任务id */
    private Integer taskId;
    
    /** 任务类型 2: 主机漏扫任务  1: web漏扫任务 */
    private Integer taskType;
    
    /** 任务状态: host: 1:正在调度，2:任务运行中，3:任务异常，4:扫描完成 */
    /** 任务状态: web:  0:正在调度，1:任务运行中，2:扫描完成，3:任务失败, 4: 任务中止 */
    private Integer taskStatus;
    
    /** 任务进度: 0-100 */
    private Integer finishRate;
    
    /** 高风险漏洞数 */
    private Integer highRiskNum;
    
    /** 中风险漏洞数 */
    private Integer middleRiskNum;
    
    /** 低风险漏洞数 */
    private Integer lowRiskNum;
}
```

## 📊 核心架构总结

### 模块分工
- **aqsoc-quartz**: 负责定时任务的调度和管理
- **aqsoc-monitor**: 负责扫描任务的监控和进度同步

### 关键技术点
1. **Quartz 调度框架**: 用于定时任务的创建和执行
2. **Cron 表达式**: 控制任务执行时间
3. **后台监控线程**: 每5秒同步扫描进度
4. **状态管理**: 通过 period 字段和状态变更实现"立即执行"

### 数据流转
1. 用户创建定时任务 → sys_job 表
2. Quartz 调度器执行任务 → 创建扫描任务
3. 扫描任务信息存储 → ffsafe_scantask_summary 表
4. 后台线程每5秒更新进度 → 前端实时显示

## 🎯 问题解答总结

1. **"立即执行"任务实际执行频率**: 执行一次后自动暂停，通过 `period=0` 和特殊状态管理实现
2. **扫描进度同步频率**: 每5秒通过后台监控线程自动同步，确保前端实时显示扫描进度

---

*分析完成时间: 2025-01-30*  
*分析工具: Claude 4.0 sonnet*
