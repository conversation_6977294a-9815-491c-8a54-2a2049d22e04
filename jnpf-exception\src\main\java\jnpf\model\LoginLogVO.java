package jnpf.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/16 10:10
 */
@Data
public class LoginLogVO {
    @Schema(description = "id")
    private String id;
    @Schema(description = "创建时间",example = "1")
    private Long creatorTime;
    @Schema(description = "登陆用户")
    private String userName;
    @Schema(description = "登陆IP")
    private String ipaddress;
    @Schema(description = "登陆平台")
    private String platForm;
    @Schema(description = "登陆日志摘要")
    private String abstracts;
}
