package jnpf.message.entity;

import jnpf.base.entity.SuperEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 *
 * 短信变量表
 * @版本： V3.2.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2022-08-18
 */
@Data
@TableName("base_message_wechat_user")
public class WechatUserEntity extends SuperEntity<String> {

    /** 公众号元素id **/
    @TableField("F_GZHID")
    private String gzhId;

    /** 用户id **/
    @TableField("F_USERID")
    private String userId;

    /** 公众号用户id **/
    @TableField("F_OPENID")
    private String openId;

    /** 是否关注公众号 **/
    @TableField("F_CLOSEMARK")
    private Integer closeMark;

    /**
     * 状态
     */
    @TableField("F_ENABLEDMARK")
    private Integer enabledMark;
}
