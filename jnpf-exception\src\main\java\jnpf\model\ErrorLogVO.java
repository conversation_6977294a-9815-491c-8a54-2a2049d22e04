package jnpf.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/16 10:10
 */
@Data
public class ErrorLogVO {
    @Schema(description = "创建用户")
    private String userName;
    @Schema(description = "创建时间",example = "1")
    private Long creatorTime;
    @Schema(description = "IP")
    private String ipaddress;
    @Schema(description = "id")
    private String id;
    @Schema(description = "异常功能")
    private String moduleName;
    @Schema(description = "异常描述")
    private String json;
}
