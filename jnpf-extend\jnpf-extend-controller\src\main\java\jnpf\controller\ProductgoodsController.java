package jnpf.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.controller.SuperController;
import jnpf.base.vo.ListVO;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.entity.ProductgoodsEntity;
import jnpf.model.productgoods.*;
import jnpf.service.ProductgoodsService;
import jnpf.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品商品
 *
 * @版本： V3.1.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2021-07-10 15:57:50
 */
@Slf4j
@RestController
@Tag(name = "产品商品", description = "Goods")
@RequestMapping("/api/extend/saleOrder/Goods")
public class ProductgoodsController extends SuperController<ProductgoodsService, ProductgoodsEntity> {

    @Autowired
    private ProductgoodsService productgoodsService;

    /**
     * 列表
     *
     * @param type 类型
     * @return
     */
    @GetMapping("/getGoodList")
    @Operation(summary = "列表")
    @Parameters({
            @Parameter(name = "type", description = "类型"),
    })
    @SaCheckPermission("extend.saleOrder")
    public ActionResult<ListVO<ProductgoodsListVO>> list(@RequestParam("type")String type) {
        List<ProductgoodsEntity> list = productgoodsService.getGoodList(type);
        List<ProductgoodsListVO> listVO = JsonUtil.getJsonToList(list, ProductgoodsListVO.class);
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

    /**
     * 列表
     *
     * @param goodsPagination 分页模型
     * @return
     */
    @GetMapping
    @Operation(summary = "列表")
    @SaCheckPermission("extend.saleOrder")
    public ActionResult<PageListVO<ProductgoodsListVO>> list(ProductgoodsPagination goodsPagination) {
        List<ProductgoodsEntity> list = productgoodsService.getList(goodsPagination);
        List<ProductgoodsListVO> listVO = JsonUtil.getJsonToList(list, ProductgoodsListVO.class);
        PageListVO vo = new PageListVO();
        vo.setList(listVO);
        PaginationVO page = JsonUtil.getJsonToBean(goodsPagination, PaginationVO.class);
        vo.setPagination(page);
        return ActionResult.success(vo);
    }

    /**
     * 创建
     *
     * @param goodsCrForm 商品模型
     * @return
     */
    @PostMapping
    @Operation(summary = "创建")
    @Parameters({
            @Parameter(name = "goodsCrForm", description = "商品模型",required = true),
    })
    @SaCheckPermission("extend.saleOrder")
    public ActionResult create(@RequestBody @Valid ProductgoodsCrForm goodsCrForm) {
        ProductgoodsEntity entity = JsonUtil.getJsonToBean(goodsCrForm, ProductgoodsEntity.class);
        productgoodsService.create(entity);
        return ActionResult.success("新建成功");
    }

    /**
     * 信息
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "信息")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission("extend.saleOrder")
    public ActionResult<ProductgoodsInfoVO> info(@PathVariable("id") String id) {
        ProductgoodsEntity entity = productgoodsService.getInfo(id);
        ProductgoodsInfoVO vo = JsonUtil.getJsonToBean(entity, ProductgoodsInfoVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 更新
     *
     * @param id                主键
     * @param goodsCrFormUpForm 商品模型
     * @return
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "goodsCrFormUpForm", description = "商品模型",required = true),
    })
    @SaCheckPermission("extend.saleOrder")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid ProductgoodsUpForm goodsCrFormUpForm) {
        ProductgoodsEntity entity = JsonUtil.getJsonToBean(goodsCrFormUpForm, ProductgoodsEntity.class);
        boolean ok = productgoodsService.update(id, entity);
        if (ok) {
            return ActionResult.success("更新成功");
        }
        return ActionResult.fail("更新失败，数据不存在");
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission("extend.saleOrder")
    public ActionResult delete(@PathVariable("id") String id) {
        ProductgoodsEntity entity = productgoodsService.getInfo(id);
        if (entity != null) {
            productgoodsService.delete(entity);
        }
        return ActionResult.success("删除成功");
    }

    /**
     * 下拉
     *
     * @param goodsPagination 下拉模型
     * @return
     */
    @GetMapping("/Selector")
    @Operation(summary = "下拉")
    @SaCheckPermission("extend.saleOrder")
    public ActionResult<ListVO<ProductgoodsListVO>> listSelect(ProductgoodsPagination goodsPagination) {
        goodsPagination.setCurrentPage(1);
        goodsPagination.setPageSize(50);
        List<ProductgoodsEntity> list = productgoodsService.getList(goodsPagination);
        List<ProductgoodsListVO> listVO = JsonUtil.getJsonToList(list, ProductgoodsListVO.class);
        ListVO vo = new ListVO();
        vo.setList(listVO);
        return ActionResult.success(vo);
    }

}
