package jnpf.engine.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.base.ActionResult;
import jnpf.base.UserInfo;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.constant.MsgCode;
import jnpf.engine.entity.*;
import jnpf.engine.enums.FlowNodeEnum;
import jnpf.engine.enums.FlowRecordEnum;
import jnpf.engine.enums.FlowStatusEnum;
import jnpf.engine.enums.FlowTaskStatusEnum;
import jnpf.engine.model.flowbefore.*;
import jnpf.engine.model.flowcandidate.FlowCandidateUserModel;
import jnpf.engine.model.flowcandidate.FlowCandidateVO;
import jnpf.engine.model.flowcandidate.FlowRejectVO;
import jnpf.engine.model.flowengine.FlowModel;
import jnpf.engine.model.flowengine.FlowUpdateNode;
import jnpf.engine.model.flowengine.shuntjson.childnode.ChildNode;
import jnpf.engine.model.flowengine.shuntjson.nodejson.ChildNodeList;
import jnpf.engine.model.flowengine.shuntjson.nodejson.ConditionList;
import jnpf.engine.model.flowtask.FlowNodeListModel;
import jnpf.engine.model.flowtask.FlowTaskListModel;
import jnpf.engine.model.flowtask.PaginationFlowTask;
import jnpf.engine.model.flowtask.TaskNodeModel;
import jnpf.engine.service.*;
import jnpf.engine.util.FlowJsonUtil;
import jnpf.engine.util.FlowNature;
import jnpf.engine.util.FlowTaskUtil;
import jnpf.exception.WorkFlowException;
import jnpf.model.LoginUser;
import jnpf.model.SysUser;
import jnpf.permission.service.RuoYiSysUserService;
import jnpf.service.FormDataService;
import jnpf.permission.entity.UserEntity;
import jnpf.util.*;
import jnpf.util.context.RequestContextHolderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 待我审核
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月27日 上午9:18
 */
@Tag(name = "待我审核", description = "FlowBefore")
@RestController
@RequestMapping("/api/workflow/Engine/FlowBefore")
public class FlowBeforeController {


    @Autowired
    private ServiceAllUtil serviceUtil;
    @Autowired
    private UserProvider userProvider;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FlowTaskUtil flowTaskUtil;
    @Autowired
    private FlowTaskService flowTaskService;
    @Autowired
    private FlowTemplateJsonService flowTemplateJsonService;
    @Autowired
    private FlowTaskOperatorService flowTaskOperatorService;
    @Autowired
    private FlowTaskOperatorRecordService flowTaskOperatorRecordService;
    @Autowired
    private FlowTaskNodeService flowTaskNodeService;
    @Autowired
    private FlowTaskNewService flowTaskNewService;
    @Autowired
    private FormDataService formDataService;
    @Autowired
    private RuoYiSysUserService ruoYiSysUserService;
    @Autowired
    private FlowDynamicService flowDynamicService;

    /**
     * 获取待我审核列表
     *
     * @param category           分类
     * @param paginationFlowTask 分页模型
     * @return
     */
    @Operation(summary = "获取待我审核列表(有带分页)，1-待办事宜，2-已办事宜，3-抄送事宜,4-批量审批")
    @GetMapping("/List/{category}")
    @Parameters({
            @Parameter(name = "category", description = "分类", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate"}, mode = SaMode.OR)
    public ActionResult<PageListVO<FlowBeforeListVO>> list(@PathVariable("category") String category, PaginationFlowTask paginationFlowTask) {
        List<FlowTaskListModel> data = new ArrayList<>();
        if (FlowNature.WAIT.equals(category)) {
            data.addAll(flowTaskService.getWaitList(paginationFlowTask));
        } else if (FlowNature.TRIAL.equals(category)) {
            data.addAll(flowTaskService.getTrialList(paginationFlowTask));
        } else if (FlowNature.CIRCULATE.equals(category)) {
            data.addAll(flowTaskService.getCirculateList(paginationFlowTask));
        } else if (FlowNature.BATCH.equals(category)) {
            paginationFlowTask.setIsBatch(1);
            data.addAll(flowTaskService.getWaitList(paginationFlowTask));
        }
        List<FlowBeforeListVO> listVO = new LinkedList<>();
        List<UserEntity> userList = serviceUtil.getUserName(data.stream().map(FlowTaskListModel::getCreatorUserId).collect(Collectors.toList()));
        boolean isBatch = FlowNature.BATCH.equals(category);
        for (FlowTaskListModel task : data) {
            FlowBeforeListVO vo = JsonUtil.getJsonToBean(task, FlowBeforeListVO.class);
            //用户名称赋值
            UserEntity user = userList.stream().filter(t -> t.getId().equals(vo.getCreatorUserId())).findFirst().orElse(null);
            vo.setUserName(user != null ? user.getRealName() + "/" + user.getAccount() : "");
            if (isBatch) {
                ChildNodeList childNode = JsonUtil.getJsonToBean(vo.getApproversProperties(), ChildNodeList.class);
                vo.setApproversProperties(JsonUtil.getObjectToString(childNode.getProperties()));
            }
            vo.setFlowVersion(StringUtil.isEmpty(vo.getFlowVersion()) ? "" : vo.getFlowVersion());
            listVO.add(vo);
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(paginationFlowTask, PaginationVO.class);
        return ActionResult.page(listVO, paginationVO);
    }

    /**
     * 获取待我审批信息
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "获取待我审批信息")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    public ActionResult<FlowBeforeInfoVO> info(@PathVariable("id") String id, FlowModel flowModel) throws WorkFlowException {
        flowModel.setId(id);
        FlowBeforeInfoVO vo = flowTaskNewService.getBeforeInfo(flowModel);
        //处理当前默认值
        if (vo != null && vo.getFlowFormInfo() != null && StringUtil.isNotEmpty(vo.getFlowFormInfo().getPropertyJson()) && vo.getFlowFormInfo().getFormType() == 2) {
            UserInfo userInfo = userProvider.get();
            Map<String, Integer> havaDefaultCurrentValue = new HashMap<>();
            vo.getFlowFormInfo().setPropertyJson(formDataService.setDefaultCurrentValue(vo.getFlowFormInfo().getPropertyJson(), havaDefaultCurrentValue));
        }
        return ActionResult.success(vo);
    }

    /**
     * 待我审核审核
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核审核")
    @PostMapping("/Audit/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult audit(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        //FlowTaskOperatorEntity operator = flowTaskOperatorService.getInfo(id);

        //改为批量审批
        try {
            QueryWrapper<FlowTaskOperatorEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().and(q -> q.eq(FlowTaskOperatorEntity::getId, id).or().eq(FlowTaskOperatorEntity::getTaskId, id));
            List<FlowTaskOperatorEntity> list = flowTaskOperatorService.list(queryWrapper);
            if(CollUtil.isNotEmpty(list)){
                List<FlowTaskOperatorEntity> finalList = flowTaskOperatorService.list(new LambdaQueryWrapper<FlowTaskOperatorEntity>().eq(FlowTaskOperatorEntity::getTaskId, list.get(0).getTaskId())
                        .eq(FlowTaskOperatorEntity::getHandleId, userProvider.get().getUserId()).eq(FlowTaskOperatorEntity::getCompletion, 0));
                if(CollUtil.isNotEmpty(finalList)){
                    FlowTaskOperatorEntity firstOperator = finalList.get(0);
                    String type = firstOperator.getType();
                    List<FlowTaskOperatorEntity> matchOperatorList = finalList.stream().filter(t -> type.equals(t.getType())).collect(Collectors.toList());
                    if(CollUtil.isNotEmpty(matchOperatorList)){
                        UserInfo userInfo = userProvider.get();
                        for (FlowTaskOperatorEntity operator : matchOperatorList) {
                            flowModel.setUserInfo(userInfo);
                            FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
                            flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
                            if (FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
                                String rejecttKey = userInfo.getTenantId() + operator.getId();
                                if (redisUtil.exists(rejecttKey)) {
                                    throw new WorkFlowException(MsgCode.WF005.get());
                                }
                                redisUtil.insert(rejecttKey, operator.getId(), 10);
                                flowModel.setTaskOperatorId(operator.getId());
                                flowTaskNewService.auditAll(flowTask, operator, flowModel);
                            }
                        }
                    }
                    return ActionResult.success("审核成功");
                }else {
                    return ActionResult.fail(MsgCode.WF005.get());
                }
            }else {
                return ActionResult.fail(MsgCode.WF005.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        /*UserInfo userInfo = userProvider.get();
        flowModel.setUserInfo(userInfo);
        FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
        flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
        if (FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
            String rejecttKey = userInfo.getTenantId() + id;
            if (redisUtil.exists(rejecttKey)) {
                throw new WorkFlowException(MsgCode.WF005.get());
            }
            redisUtil.insert(rejecttKey, id, 10);
            flowTaskNewService.auditAll(flowTask, operator, flowModel);
            return ActionResult.success("审核成功");
        } else {
            return ActionResult.fail(MsgCode.WF005.get());
        }*/
    }

    /**
     * 保存草稿
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "保存草稿")
    @PostMapping("/SaveAudit/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult saveAudit(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        if("0".equals(id)){
            //新发起 txh 250627
            UserInfo userInfo = userProvider.get();
            flowModel.setUserInfo(userInfo);
            FlowModel newFlowModel = new FlowModel();
            newFlowModel.setFormData(flowModel.getFormData());
            newFlowModel.setFlowId(flowModel.getFlowId());
            newFlowModel.setStatus("-1");
            newFlowModel.setFlowUrgent(Integer.valueOf(userInfo.getUserId()));
            newFlowModel.setIsCustom(true);
            newFlowModel.setUserInfo(userInfo);
            newFlowModel.setUserId(userInfo.getUserId());
            flowDynamicService.batchCreateOrUpdate(newFlowModel);
            JSONObject resData = newFlowModel.getResData();
            if(resData != null){
                FlowTaskEntity flowTask = resData.getObject("flowTask", FlowTaskEntity.class);
                newFlowModel.setProcessId(flowTask.getId());
            }
            flowTaskNewService.submit(newFlowModel);
            /*if(resData != null){
                FlowTaskEntity flowTask = resData.getObject("flowTask", FlowTaskEntity.class);
                if(flowTask != null){
                    //新增任务节点
                    String flowTemplateJson = flowTask.getFlowTemplateJson();
                    if(StrUtil.isNotBlank(flowTemplateJson)){
                        JSONObject flowTemplateData = JsonUtil.getJsonToBean(flowTemplateJson, JSONObject.class);
                        List<JSONObject> nodeList = new ArrayList<>();
                        loopTemplateNode(flowTemplateData, nodeList);
                        if(CollUtil.isNotEmpty(nodeList)){
                            //获取流程节点
                            List<ConditionList> conditionListAll = new ArrayList<>();
                            FlowUpdateNode updateNode = FlowUpdateNode.builder().childNodeAll(childNodeAll).nodeListAll(nodeListAll).taskNodeList(taskNodeList).conditionListAll(conditionListAll).flowTask(flowTask).userInfo(userInfo).isSubmit(true).build();
                            flowTaskUtil.updateNodeList(updateNode);
                            //保存节点数据
                            FlowTaskNodeEntity startNode = taskNodeList.stream().filter(t -> FlowNature.NodeStart.equals(t.getNodeType())).findFirst().get();
                            FlowTaskOperatorEntity operatorEntity = new FlowTaskOperatorEntity();
                            operatorEntity.setId(FlowNature.ParentId);
                            operatorEntity.setNodeCode(startNode.getNodeCode());
                            FlowNodeListModel nodeListModel = new FlowNodeListModel(taskNodeList, flowModel, true, startNode, 1L);
                            flowTaskUtil.nodeListAll(nodeListModel);

                            for (int i = 0; i < nodeList.size(); i++) {
                                JSONObject curNode = nodeList.get(i);
                                FlowTaskNodeEntity taskNodeEntity = new FlowTaskNodeEntity();
                                //todo 调试看看怎么生成的nodePropertiesJSON
                                taskNodeEntity.setNodeCode(curNode.getString("nodeCode"));
                                taskNodeEntity.setNodeName(curNode.getString("title"));
                                taskNodeEntity.setNodeType(curNode.getString("nodeType"));
                            }
                        }
                    }

                }
            }*/
            return ActionResult.success(MsgCode.SU002.get());
        }
        FlowTaskOperatorEntity flowTaskOperatorEntity = flowTaskOperatorService.getInfo(id);
        if (flowTaskOperatorEntity != null) {
            Map<String, Object> formDataAll = flowModel.getFormData();
            flowTaskOperatorEntity.setDraftData(JsonUtil.getObjectToString(formDataAll));
            flowTaskOperatorService.update(flowTaskOperatorEntity);

            FlowTaskEntity flowTask = flowTaskService.getById(flowTaskOperatorEntity.getTaskId());
            if(flowTask != null){
                JSONObject flowInfo = (JSONObject) formDataAll.getOrDefault("flowInfo", new JSONObject());
                flowInfo.put("isTempSave", true);
                formDataAll.put("flowInfo",flowInfo);
                ChildNode childNode = JsonUtil.getJsonToBean(flowTask.getFlowTemplateJson(), ChildNode.class);
                serviceUtil.createOrUpdateDelegateUser(childNode.getProperties().getFormId(), flowTask.getProcessId(), formDataAll, serviceUtil.getUserInfo(userProvider.get().getUserId()));
            }

            return ActionResult.success(MsgCode.SU002.get());
        }
        return ActionResult.fail(MsgCode.FA001.get());
    }

    private void loopTemplateNode(JSONObject templateData, List<JSONObject> result){
        String type = templateData.getString("type");
        JSONObject node = new JSONObject();
        if("start".equals(type)){
            node.put("title","开始");
            node.put("nodeUp","0");
        }else if ("approver".equals(type)){
            JSONObject properties = templateData.getJSONObject("properties");
            if(properties != null){
                String title = properties.getString("title");
                node.put("title",title);
                node.put("nodeUp",properties.getString("rejectStep"));
            }
        }
        node.put("nodeCode",templateData.getString("nodeId"));
        node.put("nodeType",type);
        result.add(node);
        JSONObject childNode = templateData.getJSONObject("childNode");
        if(childNode != null){
            loopTemplateNode(childNode,result);
        }
    }

    /**
     * 审批汇总
     *
     * @param id       主键
     * @param category 类型
     * @param type     类型
     * @return
     */
    @Operation(summary = "审批汇总")
    @GetMapping("/RecordList/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<List<FlowSummary>> recordList(@PathVariable("id") String id, String category, String type) {
        List<FlowSummary> flowSummaries = flowTaskNewService.recordList(id, category, type);
        return ActionResult.success(flowSummaries);
    }

    /**
     * 待我审核驳回
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核驳回")
    @PostMapping("/Reject/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult reject(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskOperatorEntity operator = flowTaskOperatorService.getInfo(id);
        FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
        UserInfo userInfo = userProvider.get();
        flowModel.setUserInfo(userInfo);

        String rejectStep = flowModel.getRejectStep();
        if(StrUtil.isNotBlank(rejectStep)){
            List<String> rejectList = StrUtil.split(rejectStep, ",");
            List<FlowTaskNodeEntity> allNodeList = flowTaskNodeService.list(new LambdaQueryWrapper<FlowTaskNodeEntity>().eq(FlowTaskNodeEntity::getTaskId, operator.getTaskId())
                    .eq(FlowTaskNodeEntity::getCompletion, 0).in(FlowTaskNodeEntity::getNodeUp, rejectList));
            if(CollUtil.isNotEmpty(allNodeList)){
                List<FlowTaskOperatorEntity> operatorList = flowTaskOperatorService.list(new LambdaQueryWrapper<FlowTaskOperatorEntity>().eq(FlowTaskOperatorEntity::getTaskId, operator.getTaskId())
                        .eq(FlowTaskOperatorEntity::getCompletion, 0).in(FlowTaskOperatorEntity::getNodeCode, allNodeList.stream().map(FlowTaskNodeEntity::getNodeCode).collect(Collectors.toList()))
                        .eq(FlowTaskOperatorEntity::getHandleId, operator.getHandleId()));
                if(CollUtil.isNotEmpty(operatorList)){
                    for (FlowTaskOperatorEntity operatorEntity : operatorList) {
                        allNodeList.stream().filter(node -> node.getNodeCode().equals(operatorEntity.getNodeCode())).findFirst().ifPresent(node -> {
                            flowModel.setRejectStep(node.getNodeUp());
                        });
                        flowTaskNewService.permissions(operatorEntity.getHandleId(), flowTask, operatorEntity, "", flowModel);
                        if (FlowNature.ProcessCompletion.equals(operatorEntity.getCompletion())) {
                            String rejecttKey = userInfo.getTenantId() + operatorEntity.getId();
                            if (redisUtil.exists(rejecttKey)) {
                                throw new WorkFlowException(MsgCode.WF112.get());
                            }
                            redisUtil.insert(rejecttKey, operatorEntity.getId(), 10);
                            flowTaskNewService.rejectAll(flowTask, operatorEntity, flowModel);
                        }
                    }
                    return ActionResult.success("退回成功");
                }
            }
        }

        flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
        if (FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
            String rejecttKey = userInfo.getTenantId() + id;
            if (redisUtil.exists(rejecttKey)) {
                throw new WorkFlowException(MsgCode.WF112.get());
            }
            redisUtil.insert(rejecttKey, id, 10);
            flowTaskNewService.rejectAll(flowTask, operator, flowModel);
            return ActionResult.success("退回成功");
        } else {
            return ActionResult.fail("已审核完成");
        }
    }

    /**
     * 待我审核转办
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核转办")
    @PostMapping("/Transfer/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult transfer(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskOperatorEntity operator = flowTaskOperatorService.getInfo(id);
        FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
        operator.setHandleId(flowModel.getFreeApproverUserId());
        operator.setCreatorTime(new Date());
        flowTaskNewService.transfer(operator, flowModel);
        return ActionResult.success("转办成功");
    }

    /**
     * 待我审核转办
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核加签")
    @PostMapping("/freeApprover/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult freeApprover(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskOperatorEntity operator = flowTaskOperatorService.getInfo(id);
        UserInfo userInfo = userProvider.get();
        flowModel.setUserInfo(userInfo);
        FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
        flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
        if (FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
            String rejecttKey = userInfo.getTenantId() + id;
            if (redisUtil.exists(rejecttKey)) {
                throw new WorkFlowException(MsgCode.WF005.get());
            }
            redisUtil.insert(rejecttKey, id, 10);
            flowTaskNewService.auditAll(flowTask, operator, flowModel);
        }
        return ActionResult.success("加签成功");
    }

    /**
     * 待我审核撤回审核
     * 注意：在撤销流程时要保证你的下一节点没有处理这条记录；如已处理则无法撤销流程。
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核撤回审核")
    @PostMapping("/Recall/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult recall(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskOperatorRecordEntity operatorRecord = flowTaskOperatorRecordService.getInfo(id);
        List<FlowTaskNodeEntity> nodeList = flowTaskNodeService.getList(operatorRecord.getTaskId()).stream().filter(t -> FlowNodeEnum.Process.getCode().equals(t.getState())).collect(Collectors.toList());
        FlowTaskNodeEntity taskNode = nodeList.stream().filter(t -> t.getId().equals(operatorRecord.getTaskNodeId())).findFirst().orElse(null);
        if(taskNode == null || FlowRecordEnum.revoke.getCode().equals(operatorRecord.getStatus())){
            return ActionResult.fail("流程已撤回，不能重复操作！");
        }
        if (taskNode != null && !FlowRecordEnum.revoke.getCode().equals(operatorRecord.getStatus())) {
            flowModel.setUserInfo(userProvider.get());
            flowTaskNewService.recall(id, operatorRecord, flowModel);
        }
        return ActionResult.success("撤回成功");
    }

    /**
     * 待我审核终止审核
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "待我审核终止审核")
    @PostMapping("/Cancel/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult cancel(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskEntity flowTaskEntity = flowTaskService.getInfo(id);
        if (flowTaskEntity != null) {
            if (flowTaskEntity.getFlowType() == 1) {
                return ActionResult.fail("功能流程不能终止");
            }
            flowModel.setUserInfo(userProvider.get());
            flowTaskNewService.cancel(flowTaskEntity, flowModel);
            return ActionResult.success(MsgCode.SU009.get());
        }
        return ActionResult.fail(MsgCode.FA009.get());
    }

    /**
     * 指派人
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "指派人")
    @PostMapping("/Assign/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult assign(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.assign(id, flowModel);
        return ActionResult.success("指派成功");
    }

    /**
     * 获取候选人
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "获取候选人节点")
    @PostMapping("/Candidates/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<FlowCandidateVO> candidates(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        flowModel.setUserInfo(userProvider.get());
        FlowCandidateVO candidate = flowTaskNewService.candidates(id, flowModel, false);
        return ActionResult.success(candidate);
    }

    /**
     * 获取候选人
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "获取候选人")
    @PostMapping("/CandidateUser/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<PageListVO<FlowCandidateUserModel>> candidateUser(@PathVariable("id") String id, @RequestBody FlowModel flowModel) throws WorkFlowException {
        flowModel.setUserInfo(userProvider.get());
        List<FlowCandidateUserModel> candidate = flowTaskNewService.candidateUser(id, flowModel);
        PaginationVO paginationVO = JsonUtil.getJsonToBean(flowModel, PaginationVO.class);
        return ActionResult.page(candidate, paginationVO);
    }

    /**
     * 批量审批引擎
     *
     * @return
     */
    @Operation(summary = "批量审批引擎")
    @GetMapping("/BatchFlowSelector")
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<List<FlowBatchModel>> batchFlowSelector() {
        List<FlowBatchModel> batchFlowList = flowTaskService.batchFlowSelector();
        return ActionResult.success(batchFlowList);
    }

    /**
     * 拒绝下拉框
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "拒绝下拉框")
    @GetMapping("/RejectList/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<FlowRejectVO> rejectList(@PathVariable("id") String id) throws WorkFlowException {
        FlowRejectVO vo = flowTaskNewService.rejectList(id, false);
        return ActionResult.success(vo);
    }

    /**
     * 引擎节点
     *
     * @param id 主键
     * @return
     * @throws WorkFlowException
     */
    @Operation(summary = "引擎节点")
    @GetMapping("/NodeSelector/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<List<FlowBatchModel>> nodeSelector(@PathVariable("id") String id) throws WorkFlowException {
        FlowTemplateAllModel template = flowTaskUtil.templateJson(id);
        String templateJson = template.getTemplateJson().getFlowTemplateJson();
        List<FlowBatchModel> batchList = new ArrayList<>();
        ChildNode childNodeAll = JsonUtil.getJsonToBean(templateJson, ChildNode.class);
        //获取流程节点
        List<ChildNodeList> nodeListAll = new ArrayList<>();
        List<ConditionList> conditionListAll = new ArrayList<>();
        //递归获取条件数据和节点数据
        FlowJsonUtil.getTemplateAll(childNodeAll, nodeListAll, conditionListAll);
        List<String> type = new ArrayList() {{
            add(FlowNature.NodeSubFlow);
            add(FlowNature.NodeStart);
        }};
        for (ChildNodeList childNodeList : nodeListAll) {
            if (!type.contains(childNodeList.getCustom().getType())) {
                FlowBatchModel batchModel = new FlowBatchModel();
                batchModel.setFullName(childNodeList.getProperties().getTitle());
                batchModel.setId(childNodeList.getCustom().getNodeId());
                batchList.add(batchModel);
            }
        }
        return ActionResult.success(batchList);
    }

    /**
     * 流程批量类型下拉
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "流程批量类型下拉")
    @GetMapping("/BatchFlowJsonList/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<List<FlowBatchModel>> batchFlowJsonList(@PathVariable("id") String id) {
        List<String> taskIdList = flowTaskOperatorService.getBatchList().stream().map(FlowTaskOperatorEntity::getTaskId).collect(Collectors.toList());
        List<FlowTaskEntity> taskListAll = flowTaskService.getOrderStaList(taskIdList);
        List<String> flowIdList = taskListAll.stream().filter(t -> t.getTemplateId().equals(id)).map(FlowTaskEntity::getFlowId).collect(Collectors.toList());
        List<FlowTemplateJsonEntity> templateJsonList = flowTemplateJsonService.getTemplateJsonList(flowIdList);
        List<FlowBatchModel> listVO = new ArrayList<>();
        for (FlowTemplateJsonEntity entity : templateJsonList) {
            FlowBatchModel vo = JsonUtil.getJsonToBean(entity, FlowBatchModel.class);
            vo.setFullName(vo.getFullName() + "(v" + entity.getVersion() + ")");
            listVO.add(vo);
        }
        return ActionResult.success(listVO);
    }

    /**
     * 批量审批
     *
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "批量审批")
    @PostMapping("/BatchOperation")
    @Parameters({
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult batchOperation(@RequestBody FlowModel flowModel) throws WorkFlowException {
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.batch(flowModel);
        return ActionResult.success("批量操作完成");
    }

    /**
     * 批量获取候选人
     *
     * @param flowId         流程主键
     * @param taskOperatorId 代办主键
     * @return
     * @throws WorkFlowException
     */
    @Operation(summary = "批量获取候选人")
    @GetMapping("/BatchCandidate")
    @Parameters({
            @Parameter(name = "flowId", description = "流程主键", required = true),
            @Parameter(name = "taskOperatorId", description = "代办主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<FlowCandidateVO> batchCandidate(String flowId, String taskOperatorId) throws WorkFlowException {
        FlowModel flowModel = new FlowModel();
        flowModel.setUserInfo(userProvider.get());
        flowModel.setFlowId(flowId);
        FlowCandidateVO candidate = flowTaskNewService.batchCandidates(flowId, taskOperatorId, flowModel);
        return ActionResult.success(candidate);
    }

    /**
     * 消息跳转工作流
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "消息跳转工作流")
    @GetMapping("/{id}/Info")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    public ActionResult taskOperatorId(@PathVariable("id") String id) throws WorkFlowException {
        FlowTaskOperatorEntity operator = flowTaskOperatorService.getInfo(id);
        FlowTaskEntity flowTask = flowTaskService.getInfo(operator.getTaskId());
        FlowModel flowModel = new FlowModel();
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.permissions(operator.getHandleId(), flowTask, operator, "", flowModel);
        Map<String, Object> map = new HashMap<>();
        if (!FlowNature.ProcessCompletion.equals(operator.getCompletion())) {
            map.put("isCheck", true);
        } else {
            map.put("isCheck", false);
        }
        List<FlowTaskListModel> data = flowTaskService.getTrialList();
        if (data != null && data.size() > 0) {
            List<String> taskNodeId = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                FlowTaskListModel taskListModel = data.get(i);
                String nodeId = taskListModel.getThisStepId();
                if (taskNodeId.contains(nodeId)) {
                    data.remove(i);
                }
                taskNodeId.add(nodeId);
            }
            FlowTaskListModel taskListModel = data.stream().filter(t -> t.getThisStepId().equals(operator.getTaskNodeId())).findFirst().orElse(null);
            if (taskListModel != null) {
                map.put("flowTaskOperatorRecordId", taskListModel.getId());
            }
        }
        return ActionResult.success(map);
    }

    /**
     * 节点下拉框
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "节点下拉框")
    @GetMapping("/Selector/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult<List<TaskNodeModel>> selector(@PathVariable("id") String id) {
        List<String> nodetype = new ArrayList() {{
            add(FlowNature.NodeStart);
            add(FlowNature.NodeSubFlow);
            add(FlowNature.EndRound);
        }};
        List<FlowTaskNodeEntity> list = flowTaskNodeService.getList(id).stream().filter(t -> FlowNodeEnum.Process.getCode().equals(t.getState())).collect(Collectors.toList());
        flowTaskUtil.nodeList(list);
        list = list.stream().filter(t -> !nodetype.contains(t.getNodeType())).collect(Collectors.toList());
        List<TaskNodeModel> nodeList = JsonUtil.getJsonToList(list, TaskNodeModel.class);
        return ActionResult.success(nodeList);
    }

    /**
     * 变更或者复活
     *
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "变更或者复活")
    @PostMapping("/Change")
    @Parameters({
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult change(@RequestBody FlowModel flowModel) throws WorkFlowException {
        FlowTaskEntity info = flowTaskService.getInfo(flowModel.getTaskId());
        if (FlowTaskStatusEnum.Revoke.getCode().equals(info.getStatus()) || FlowTaskStatusEnum.Cancel.getCode().equals(info.getStatus()) || FlowTaskStatusEnum.Draft.getCode().equals(info.getStatus())) {
            throw new WorkFlowException("该流程不能操作");
        }
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.change(flowModel);
        String msg = flowModel.getResurgence() ? "复活成功" : "变更成功";
        return ActionResult.success(msg);
    }

    /**
     * 子流程数据
     *
     * @param id 主键
     * @return
     */
    @Operation(summary = "子流程数据")
    @GetMapping("/SubFlowInfo/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    public ActionResult<List<FlowBeforeInfoVO>> subFlowInfo(@PathVariable("id") String id) throws WorkFlowException {
        FlowTaskNodeEntity taskNode = flowTaskNodeService.getInfo(id);
        List<FlowBeforeInfoVO> listVO = new ArrayList<>();
        if (taskNode != null) {
            ChildNodeList childNodeList = JsonUtil.getJsonToBean(taskNode.getNodePropertyJson(), ChildNodeList.class);
            List<String> flowTaskIdList = new ArrayList<>();
            flowTaskIdList.addAll(childNodeList.getCustom().getAsyncTaskList());
            flowTaskIdList.addAll(childNodeList.getCustom().getTaskId());
            for (String taskId : flowTaskIdList) {
                FlowModel flowModel = new FlowModel();
                flowModel.setId(taskId);
                FlowBeforeInfoVO vo = flowTaskNewService.getBeforeInfo(flowModel);
                listVO.add(vo);
            }
        }
        return ActionResult.success(listVO);
    }

    /**
     * 流程类型下拉
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "流程类型下拉")
    @GetMapping("/Suspend/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult suspend(@PathVariable("id") String id) {
        List<FlowTaskEntity> childList = flowTaskService.getChildList(id, FlowTaskEntity::getId, FlowTaskEntity::getIsAsync);
        boolean isAsync = childList.stream().filter(t -> FlowNature.ChildAsync.equals(t.getIsAsync())).count() > 0;
        return ActionResult.success(isAsync);
    }

    /**
     * 流程挂起
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "流程挂起")
    @PostMapping("/Suspend/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult suspend(@PathVariable("id") String id, @RequestBody FlowModel flowModel) {
        flowModel.setUserInfo(userProvider.get());
        flowTaskNewService.suspend(id, flowModel, true);
        return ActionResult.success("挂起成功");
    }

    /**
     * 流程恢复
     *
     * @param id        主键
     * @param flowModel 流程模型
     * @return
     */
    @Operation(summary = "流程恢复")
    @PostMapping("/Restore/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "flowModel", description = "流程模型", required = true),
    })
    @SaCheckPermission(value = {"workFlow.flowTodo", "workFlow.flowDone", "workFlow.flowCirculate", "workFlow.flowMonitor"}, mode = SaMode.OR)
    public ActionResult restore(@PathVariable("id") String id, @RequestBody FlowModel flowModel) {
        flowModel.setUserInfo(userProvider.get());
        flowModel.setSuspend(false);
        flowTaskNewService.suspend(id, flowModel, false);
        return ActionResult.success("恢复成功");
    }

    @PostMapping("/getTaskOperatorUserList/{id}")
    public ActionResult getTaskOperatorUserList(@PathVariable("id") String id) {
        List<String> userList = new ArrayList<>();
        List<FlowTaskOperatorEntity> list = flowTaskOperatorService.list(new LambdaQueryWrapper<FlowTaskOperatorEntity>().eq(FlowTaskOperatorEntity::getTaskId, id));
        if(CollUtil.isNotEmpty(list)){
            userList = list.stream().filter(item -> item.getCompletion() == 0).map(FlowTaskOperatorEntity::getHandleId).collect(Collectors.toList());
        }
        return ActionResult.success("成功", userList);
    }
}
