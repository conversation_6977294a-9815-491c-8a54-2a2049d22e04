package jnpf.permission.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 *
 * 机构分级管理员
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("base_organizeadministrator")
public class OrganizeAdministratorEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    @TableField("F_USERID")
    private String userId;

    @TableField("F_ORGANIZEID")
    private String organizeId;

    @TableField("F_ORGANIZETYPE")
    private String organizeType;

    @TableField("F_THISLAYERADD")
    private Integer thisLayerAdd;

    @TableField("F_THISLAYEREDIT")
    private Integer thisLayerEdit;

    @TableField("F_THISLAYERDELETE")
    private Integer thisLayerDelete;

    @TableField("F_SUBLAYERADD")
    private Integer subLayerAdd;

    @TableField("F_SUBLAYEREDIT")
    private Integer subLayerEdit;

    @TableField("F_SUBLAYERDELETE")
    private Integer subLayerDelete;

    /**
     * 本层查看权限
     */
    @TableField("F_THISLAYERSELECT")
    private Integer thisLayerSelect;

    /**
     * 子级查看权限
     */
    @TableField("F_SUBLAYERSELECT")
    private Integer subLayerSelect;

}
