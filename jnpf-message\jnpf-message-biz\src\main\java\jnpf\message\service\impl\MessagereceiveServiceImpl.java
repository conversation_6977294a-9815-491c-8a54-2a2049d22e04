package jnpf.message.service.impl;


import jnpf.base.service.SuperServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jnpf.message.entity.MessageReceiveEntity;
import jnpf.message.mapper.MessagereceiveMapper;
import jnpf.message.service.MessagereceiveService;
import org.springframework.stereotype.Service;

/**
 * 消息接收 服务实现类
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月27日 上午9:18
 */
@Service
public class MessagereceiveServiceImpl extends SuperServiceImpl<MessagereceiveMapper, MessageReceiveEntity> implements MessagereceiveService {

}
