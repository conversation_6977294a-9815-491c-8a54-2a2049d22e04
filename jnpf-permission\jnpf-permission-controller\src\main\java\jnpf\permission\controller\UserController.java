package jnpf.permission.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import cn.xuyanwu.spring.file.storage.FileInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.annotation.UserPermission;
import jnpf.base.Page;
import jnpf.base.UserInfo;
import jnpf.base.vo.DownloadVO;
import jnpf.config.ConfigValueUtil;
import jnpf.constant.MsgCode;
import jnpf.exception.ImportException;
import jnpf.message.service.SynThirdDingTalkService;
import jnpf.message.service.SynThirdQyService;
import jnpf.permission.constant.PermissionConst;
import jnpf.permission.entity.*;
import jnpf.permission.model.user.form.UserCrForm;
import jnpf.permission.model.user.form.UserResetPasswordForm;
import jnpf.permission.model.user.form.UserUpForm;
import jnpf.permission.model.user.mod.*;
import jnpf.permission.model.user.page.PageUser;
import jnpf.permission.model.user.page.PaginationUser;
import jnpf.permission.model.user.vo.*;
import jnpf.permission.rest.PullUserUtil;
import jnpf.permission.util.PermissionUtil;
import jnpf.util.*;
import jnpf.base.ActionResult;
import jnpf.base.vo.PageListVO;
import jnpf.base.Pagination;
import jnpf.base.vo.PaginationVO;
import jnpf.base.vo.ListVO;
import jnpf.exception.DataException;
import jnpf.permission.service.*;
import jnpf.util.treeutil.SumTree;
import jnpf.util.treeutil.newtreeutil.TreeDotUtils;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Tag(name = "用户管理", description = "Users")
@Slf4j
@RestController
@RequestMapping("/api/permission/Users")
public class UserController extends SuperController<UserService, UserEntity> {

    @Autowired
    private CacheKeyUtil cacheKeyUtil;
    @Autowired
    private SynThirdQyService synThirdQyService;
    @Autowired
    private SynThirdDingTalkService synThirdDingTalkService;
    @Autowired
    private ConfigValueUtil configValueUtil;

    /*=== the same ===*/

    @Autowired
    private UserService userService;
    @Autowired
    private UserProvider userProvider;
    @Autowired
    private OrganizeService organizeService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserRelationService userRelationService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private OrganizeRelationService organizeRelationService;
    @Autowired
    private OrganizeAdministratorService organizeAdministratorService;
    @Autowired
    private PositionService positionService;
    @Autowired
    private GroupService groupService;

    /**
     * 取出线程池
     */
    @Autowired
    private Executor threadPoolExecutor;

    /**
     * 获取用户列表
     *
     * @param pagination 分页参数
     * @return ignore
     */
    @Operation(summary = "获取用户列表")
    @GetMapping
    public ActionResult<PageListVO<UserListVO>> getList(PaginationUser pagination) {
        List<UserEntity> userList = userService.getList(pagination, pagination.getOrganizeId(), false, true);
        List<UserListVO> list = new ArrayList<>();
        for (UserEntity userEntity : userList) {
            UserListVO userVO = JsonUtil.getJsonToBean(userEntity, UserListVO.class);
            userVO.setHeadIcon(UploaderUtil.uploaderImg(userVO.getHeadIcon()));
            // 时间小于当前时间则判断已解锁
            if (userVO.getEnabledMark() != null && userVO.getEnabledMark() != 0) {
                if (Objects.nonNull(userEntity.getUnlockTime()) && userEntity.getUnlockTime().getTime() > System.currentTimeMillis()) {
                    userVO.setEnabledMark(2);
                } else if (Objects.nonNull(userEntity.getUnlockTime()) && userEntity.getUnlockTime().getTime() < System.currentTimeMillis()) {
                    userVO.setEnabledMark(1);
                }
            }
            List<UserRelationEntity> orgRelationByUserId = userRelationService.getAllOrgRelationByUserId(userEntity.getId());
            // 储存组织id信息
            StringBuilder organize = new StringBuilder();
            for (UserRelationEntity userRelationEntity : orgRelationByUserId) {
                // 获取组织id详情
                OrganizeEntity entity = organizeService.getInfo(userRelationEntity.getObjectId());
                if (entity != null) {
                    // 获取到组织树
                    String organizeIdTree = entity.getOrganizeIdTree();
                    if (StringUtil.isNotEmpty(organizeIdTree)) {
                        // 获取到组织id集合
                        String[] orgId = organizeIdTree.split(",");
                        List<OrganizeEntity> organizeName = organizeService.getOrganizeNameSort(Arrays.asList(orgId));
                        // 单个组织储存在这里
                        StringBuilder orgName = new StringBuilder();
                        for (OrganizeEntity organizeEntity : organizeName) {
                            orgName.append("/" + organizeEntity.getFullName());
                        }
                        if (orgName.length() > 0) {
                            organize.append(";" + orgName.toString().replaceFirst("/", ""));
                        }
                    }
                }
            }
            if (organize.length() > 0) {
                userVO.setOrganize(organize.toString().replaceFirst(";", ""));
            }
            list.add(userVO);
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取用户列表
     *
     * @return ignore
     */
    @Operation(summary = "获取所有用户列表")
    @GetMapping("/All")
    public ActionResult<ListVO<UserAllVO>> getAllUsers(Pagination pagination) {
        List<UserEntity> list = userService.getList(pagination, null, false, false);
        List<UserAllVO> user = JsonUtil.getJsonToList(list, UserAllVO.class);
        ListVO<UserAllVO> vo = new ListVO<>();
        vo.setList(user);
        return ActionResult.success(vo);
    }

    /**
     * IM通讯获取用户接口
     *
     * @param pagination 分页参数
     * @return ignore
     */
    @Operation(summary = "IM通讯获取用户")
    @GetMapping("/ImUser")
    public ActionResult<PageListVO<ImUserListVo>> getAllImUserUsers(Pagination pagination) {
        List<UserEntity> data = userService.getList(pagination, true);
        List<ImUserListVo> list = new ArrayList<>();
        List<OrganizeEntity> organizeList = organizeService.getOrganizeName(data.stream().map(t -> t.getOrganizeId()).collect(Collectors.toList()));
        for (UserEntity entity : data) {
            ImUserListVo user = JsonUtil.getJsonToBean(entity, ImUserListVo.class);
            OrganizeEntity organize = organizeList.stream().filter(t -> t.getId().equals(entity.getOrganizeId())).findFirst().orElse(null);
            user.setDepartment(organize != null ? organize.getFullName() : "");
            user.setHeadIcon(UploaderUtil.uploaderImg(entity.getHeadIcon()));
            list.add(user);
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
        return ActionResult.page(list, paginationVO);
    }

    /**
     * 获取用户下拉框列表
     *
     * @return ignore
     */
    @Operation(summary = "获取用户下拉框列表")
    @GetMapping("/Selector")
    public ActionResult<ListVO<UserSelectorVO>> selector() {
        List<OrganizeEntity> allOrganizeData = organizeService.getList();
        List<OrganizeEntity> organizeData = organizeService.getList().stream().filter(
                t -> "1".equals(String.valueOf(t.getEnabledMark()))
        ).collect(Collectors.toList());
        List<UserEntity> userData = userService.getList().stream().filter(
                t -> "1".equals(String.valueOf(t.getEnabledMark()))
        ).collect(Collectors.toList());
        List<UserSelectorModel> treeList = JsonUtil.getJsonToList(organizeData, UserSelectorModel.class);
        for (UserSelectorModel entity1 : treeList) {
            if ("department".equals(entity1.getType())) {
                entity1.setIcon("icon-ym icon-ym-tree-department1");
            } else if ("company".equals(entity1.getType())) {
                entity1.setIcon("icon-ym icon-ym-tree-organization3");
            }
        }
        for (UserEntity entity : userData) {
            UserSelectorModel treeModel = new UserSelectorModel();
            treeModel.setId(entity.getId());
            treeModel.setParentId(entity.getOrganizeId());
            treeModel.setFullName(entity.getRealName() + "/" + entity.getAccount());
            treeModel.setType("user");
            treeModel.setIcon("icon-ym icon-ym-tree-user2");
            treeList.add(treeModel);
        }
        List<SumTree<UserSelectorModel>> trees = TreeDotUtils.convertListToTreeDot(treeList);
        List<UserSelectorVO> listvo = JsonUtil.getJsonToList(trees, UserSelectorVO.class);
        List<OrganizeEntity> entities = allOrganizeData.stream().filter(
                t -> "1".equals(String.valueOf(t.getEnabledMark())) && "-1".equals(t.getParentId())
        ).collect(Collectors.toList());
        Iterator<UserSelectorVO> iterator = listvo.iterator();
        while (iterator.hasNext()) {
            UserSelectorVO userSelectorVO = iterator.next();
            for (OrganizeEntity entity : entities) {
                if (entity.getId().equals(userSelectorVO.getParentId())) {
                    iterator.remove();//使用迭代器的删除方法删除
                }
            }
        }
        ListVO<UserSelectorVO> vo = new ListVO<>();
        vo.setList(listvo);
        return ActionResult.success(vo);
    }

    /**
     * 通过部门、岗位、用户、角色、分组id获取用户列表
     *
     * @param userConditionModel 用户选择模型
     * @return
     */
    @Operation(summary = "通过部门、岗位、用户、角色、分组id获取用户列表")
    @Parameters({
            @Parameter(name = "userConditionModel", description = "用户选择模型", required = true)
    })
    @PostMapping("/UserCondition")
    public ActionResult userCondition(@RequestBody UserConditionModel userConditionModel) {
        List<String> list = new ArrayList<>(16);
        if (userConditionModel.getDepartIds() != null) {
            list.addAll(userConditionModel.getDepartIds());
        }
        if (userConditionModel.getRoleIds() != null) {
            list.addAll(userConditionModel.getRoleIds());
        }
        if (userConditionModel.getPositionIds() != null) {
            list.addAll(userConditionModel.getPositionIds());
        }
        if (userConditionModel.getGroupIds() != null) {
            list.addAll(userConditionModel.getGroupIds());
        }
        if (list.size() == 0) {
            list = userRelationService.getListByObjectType(userConditionModel.getType()).stream().map(UserRelationEntity::getObjectId).collect(Collectors.toList());
            if (PermissionConst.GROUP.equals(userConditionModel.getType())) {
                List<GroupEntity> groupList = groupService.getListByIds(list, true);
                list = groupList.stream().map(GroupEntity::getId).collect(Collectors.toList());
            }
            if (PermissionConst.ORGANIZE.equals(userConditionModel.getType())) {
                List<OrganizeEntity> orgList = organizeService.getOrgEntityList(list, true);
                list = orgList.stream().map(OrganizeEntity::getId).collect(Collectors.toList());
            }
            if (PermissionConst.ROLE.equals(userConditionModel.getType())) {
                List<RoleEntity> roleList = roleService.getListByIds(list);
                list = roleList.stream().filter(t -> t.getEnabledMark() == 1).map(RoleEntity::getId).collect(Collectors.toList());
            }
            if (PermissionConst.POSITION.equals(userConditionModel.getType())) {
                List<PositionEntity> positionList = positionService.getPosList(list);
                list = positionList.stream().filter(t -> t.getEnabledMark() == 1).map(PositionEntity::getId).collect(Collectors.toList());
            }
        }
        List<String> collect = userRelationService.getListByObjectIdAll(list).stream().map(UserRelationEntity::getUserId).collect(Collectors.toList());
        if (userConditionModel.getUserIds() != null) {
            collect.addAll(userConditionModel.getUserIds());
        }
        collect = collect.stream().distinct().collect(Collectors.toList());
        List<UserEntity> userName = userService.getUserName(collect, userConditionModel.getPagination());
        List<UserIdListVo> jsonToList = JsonUtil.getJsonToList(userName, UserIdListVo.class);
        jsonToList.forEach(t -> {
            t.setHeadIcon(UploaderUtil.uploaderImg(t.getHeadIcon()));
            t.setFullName(t.getRealName() + "/" + t.getAccount());
            List<UserRelationEntity> listByUserId = userRelationService.getListByUserId(t.getId(), PermissionConst.ORGANIZE);
            List<String> orgId = listByUserId.stream().map(UserRelationEntity::getObjectId).collect(Collectors.toList());
            List<OrganizeEntity> organizeName = organizeService.getOrganizeName(orgId);
            StringBuilder stringBuilder = new StringBuilder();
            organizeName.forEach(org -> {
                if (StringUtil.isNotEmpty(org.getOrganizeIdTree())) {
                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(org.getOrganizeIdTree(), "/");
                    stringBuilder.append(",");
                    stringBuilder.append(fullNameByOrgIdTree);
                }
            });
            if (stringBuilder.length() > 0) {
                t.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
            }
        });
        PaginationVO paginationVO = JsonUtil.getJsonToBean(userConditionModel.getPagination(), PaginationVO.class);
        return ActionResult.page(jsonToList, paginationVO);
    }

    /**
     * 获取用户下拉框列表
     *
     * @param organizeIdForm 组织id
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "获取用户下拉框列表")
    @Parameters({
            @Parameter(name = "organizeId", description = "组织id", required = true),
            @Parameter(name = "pagination", description = "分页模型", required = true)
    })
    @PostMapping("/ImUser/Selector/{organizeId}")
    public ActionResult<?> imUserSelector(@PathVariable("organizeId") String organizeIdForm, @RequestBody Pagination pagination) {
        String organizeId = XSSEscape.escape(organizeIdForm);
        List<UserSelectorVO> jsonToList = new ArrayList<>();
        //判断是否搜索关键字
        if (StringUtil.isNotEmpty(pagination.getKeyword())) {
            //通过关键字查询
            List<UserEntity> list = userService.getList(pagination, false);
            //遍历用户给要返回的值插入值
            for (UserEntity entity : list) {
                UserSelectorVO vo = JsonUtil.getJsonToBean(entity, UserSelectorVO.class);
                vo.setParentId(entity.getOrganizeId());
                vo.setFullName(entity.getRealName() + "/" + entity.getAccount());
                vo.setType("user");
                vo.setIcon("icon-ym icon-ym-tree-user2");
                vo.setHeadIcon(UploaderUtil.uploaderImg(vo.getHeadIcon()));
                List<UserRelationEntity> listByUserId = userRelationService.getListByUserId(entity.getId()).stream().filter(t -> t != null && PermissionConst.ORGANIZE.equals(t.getObjectType())).collect(Collectors.toList());
                StringBuilder stringBuilder = new StringBuilder();
                listByUserId.forEach(t -> {
                    OrganizeEntity organizeEntity = organizeService.getInfo(t.getObjectId());
                    if (organizeEntity != null) {
                        String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                        if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                            stringBuilder.append("," + fullNameByOrgIdTree);
                        }
                    }
                });
                if (stringBuilder.length() > 0) {
                    vo.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
                }
                vo.setHasChildren(false);
                vo.setIsLeaf(true);
                jsonToList.add(vo);
            }
            PaginationVO jsonToBean = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
            return ActionResult.page(jsonToList, jsonToBean);
        }
        //获取所有组织
        List<OrganizeEntity> collect = organizeService.getList().stream().filter(t -> t.getEnabledMark() == 1).collect(Collectors.toList());
        //判断时候传入组织id
        //如果传入组织id，则取出对应的子集
        if (!"0".equals(organizeId)) {
            //通过组织查询部门及人员
            //单个组织
            List<OrganizeEntity> list = collect.stream().filter(t -> organizeId.equals(t.getId())).collect(Collectors.toList());
            if (list.size() > 0) {
                //获取组织信息
                OrganizeEntity organizeEntity = list.get(0);
                //取出组织下的部门
                List<OrganizeEntity> collect1 = collect.stream().filter(t -> t.getParentId().equals(organizeEntity.getId())).collect(Collectors.toList());
                for (OrganizeEntity entitys : collect1) {
                    UserSelectorVO vo = JsonUtil.getJsonToBean(entitys, UserSelectorVO.class);
                    if ("department".equals(entitys.getCategory())) {
                        vo.setIcon("icon-ym icon-ym-tree-department1");
                    } else if ("company".equals(entitys.getCategory())) {
                        vo.setIcon("icon-ym icon-ym-tree-organization3");
                    }
                    vo.setOrganize(organizeService.getFullNameByOrgIdTree(entitys.getOrganizeIdTree(), "/"));
                    // 判断组织下是否有人
                    jsonToList.add(vo);
                    vo.setHasChildren(true);
                    vo.setIsLeaf(false);
                }
                //取出组织下的人员
                List<UserEntity> entityList = userService.getListByOrganizeId(organizeId, null);
                for (UserEntity entity : entityList) {
                    if ("0".equals(String.valueOf(entity.getEnabledMark()))) {
                        continue;
                    }
                    UserSelectorVO vo = JsonUtil.getJsonToBean(entity, UserSelectorVO.class);
                    vo.setParentId(organizeId);
                    vo.setFullName(entity.getRealName() + "/" + entity.getAccount());
                    vo.setType("user");
                    vo.setIcon("icon-ym icon-ym-tree-user2");
                    List<UserRelationEntity> listByUserId = userRelationService.getListByUserId(entity.getId()).stream().filter(t -> t != null && PermissionConst.ORGANIZE.equals(t.getObjectType())).collect(Collectors.toList());
                    StringBuilder stringBuilder = new StringBuilder();
                    listByUserId.forEach(t -> {
                        OrganizeEntity organizeEntity1 = organizeService.getInfo(t.getObjectId());
                        if (organizeEntity1 != null) {
                            String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(organizeEntity1.getOrganizeIdTree(), "/");
                            if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                                stringBuilder.append("," + fullNameByOrgIdTree);
                            }
                        }
                    });
                    if (stringBuilder.length() > 0) {
                        vo.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
                    }
                    vo.setHeadIcon(UploaderUtil.uploaderImg(vo.getHeadIcon()));
                    vo.setHasChildren(false);
                    vo.setIsLeaf(true);
                    jsonToList.add(vo);
                }
            }
            ListVO<UserSelectorVO> vo = new ListVO<>();
            vo.setList(jsonToList);
            return ActionResult.success(vo);
        }

        //如果没有组织id，则取出所有组织
        List<OrganizeEntity> organizeEntityList = collect.stream().filter(t -> "-1".equals(t.getParentId())).collect(Collectors.toList());
        jsonToList = JsonUtil.getJsonToList(organizeEntityList, UserSelectorVO.class);
        //添加图标
        for (UserSelectorVO userSelectorVO : jsonToList) {
            userSelectorVO.setIcon("icon-ym icon-ym-tree-organization3");
            userSelectorVO.setHasChildren(true);
            userSelectorVO.setIsLeaf(false);
            userSelectorVO.setOrganize(organizeService.getFullNameByOrgIdTree(organizeService.getInfo(userSelectorVO.getId()).getOrganizeIdTree(), "/"));
        }
        ListVO<UserSelectorVO> vo = new ListVO<>();
        vo.setList(jsonToList);
        return ActionResult.success(vo);
    }

    /**
     * 获取用户下拉框列表
     *
     * @param organizeId 组织id
     * @param page 关键字
     * @return
     */
    @Operation(summary = "获取用户下拉框列表")
    @Parameters({
            @Parameter(name = "organizeId", description = "组织id", required = true),
            @Parameter(name = "page", description = "关键字", required = true)
    })
    @SaCheckPermission("permission.grade")
    @PostMapping("/GetListByAuthorize/{organizeId}")
    public ActionResult<ListVO<UserByRoleVO>> getListByAuthorize(@PathVariable("organizeId") String organizeId, @RequestBody Page page) {
        List<UserByRoleVO> jsonToList = userService.getListByAuthorize(organizeId, page);
        ListVO listVO = new ListVO();
        listVO.setList(jsonToList);
        return ActionResult.success(listVO);
    }

    /**
     * 获取用户信息
     *
     * @param id 用户id
     * @return ignore
     */
    @Operation(summary = "获取用户信息")
    @Parameters({
            @Parameter(name = "id", description = "用户id", required = true)
    })
    @SaCheckPermission("permission.user")
    @GetMapping("/{id}")
    public ActionResult<UserInfoVO> getInfo(@PathVariable("id") String id) throws DataException {
        UserEntity entity = userService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("用户不存在");
        }

        QueryWrapper<UserRelationEntity> roleQuery = new QueryWrapper<>();
        roleQuery.lambda().eq(UserRelationEntity::getUserId, id);
        roleQuery.lambda().eq(UserRelationEntity::getObjectType, PermissionConst.ROLE);
        List<String> roleIdList = new ArrayList<>();
        for (UserRelationEntity ure : userRelationService.list(roleQuery)) {
            roleIdList.add(ure.getObjectId());
        }

        entity.setHeadIcon(UploaderUtil.uploaderImg(entity.getHeadIcon()));
//        OrganizeEntity info = organizeService.getInfo(entity.getOrganizeId());
        // 得到组织树
        UserInfoVO vo = JsonUtilEx.getJsonToBeanEx(entity, UserInfoVO.class);
        vo.setRoleId(String.join(",", roleIdList));


        // 获取组织id数组
        QueryWrapper<UserRelationEntity> query = new QueryWrapper<>();
        query.lambda().eq(UserRelationEntity::getUserId, id);
        query.lambda().eq(UserRelationEntity::getObjectType, PermissionConst.ORGANIZE);
        List<String> organizeIds = new ArrayList<>();
        userRelationService.list(query).forEach(u -> {
            organizeIds.add(u.getObjectId());
        });

        // 岗位装配
        QueryWrapper<UserRelationEntity> positionQuery = new QueryWrapper<>();
        positionQuery.lambda().eq(UserRelationEntity::getUserId, id);
        positionQuery.lambda().eq(UserRelationEntity::getObjectType, PermissionConst.POSITION);
        String positionIds = "";
        for (UserRelationEntity ure : userRelationService.list(positionQuery)) {
            PositionEntity info = positionService.getInfo(ure.getObjectId());
            if (info != null) {
                positionIds = positionIds + "," + ure.getObjectId();
            }
        }
        if (positionIds.length() > 0) {
            vo.setPositionId(positionIds.substring(1));
        } else {
            vo.setPositionId(null);
        }
        // 设置分组id
        List<UserRelationEntity> listByObjectType = userRelationService.getListByObjectType(entity.getId(), PermissionConst.GROUP);
        StringBuilder groupId = new StringBuilder();
        listByObjectType.stream().forEach(t -> groupId.append("," + t.getObjectId()));
        if (groupId.length() > 0) {
            vo.setGroupId(groupId.toString().replaceFirst(",", ""));
        }
        vo.setOrganizeIdTree(PermissionUtil.getOrgIdsTree(organizeIds, 1, organizeService));
        return ActionResult.success(vo);
    }

    /**
     * 新建用户
     *
     * @param userCrForm 表单参数
     */
    @UserPermission
    @Operation(summary = "新建用户")
    @Parameters({
            @Parameter(name = "userCrForm", description = "表单参数", required = true)
    })
    @SaCheckPermission("permission.user")
    @PostMapping
    public ActionResult<String> create(@RequestBody @Valid UserCrForm userCrForm) throws Exception {
        UserEntity entity = JsonUtil.getJsonToBean(userCrForm, UserEntity.class);
        entity.setPassword("4a7d1ed414474e4033ac29ccb8653d9b");
        if (userService.isExistByAccount(userCrForm.getAccount())) {
            return ActionResult.fail("账户名称不能重复");
        }
        userService.create(entity);
        threadPoolExecutor.execute(() -> {
            try {
                //添加用户之后判断是否需要同步到企业微信
                synThirdQyService.createUserSysToQy(false, entity, "");
                //添加用户之后判断是否需要同步到钉钉
                synThirdDingTalkService.createUserSysToDing(false, entity, "");
            } catch (Exception e) {
                log.error("添加用户之后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
            }
        });
        String catchKey = cacheKeyUtil.getAllUser();
        if (redisUtil.exists(catchKey)) {
            redisUtil.remove(catchKey);
        }
        PullUserUtil.syncUser(entity, "create", userProvider.get().getTenantId());
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 修改用户
     *
     * @param userUpForm 表单参数
     * @param id         主键值
     */
    @UserPermission
    @Operation(summary = "修改用户")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true),
            @Parameter(name = "userUpForm", description = "表单参数", required = true)
    })
    @SaCheckPermission("permission.user")
    @PutMapping("/{id}")
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody @Valid UserUpForm userUpForm) throws Exception {
        UserEntity entity = JsonUtil.getJsonToBean(userUpForm, UserEntity.class);
        //将禁用的id加进数据
        UserEntity originUser = userService.getInfo(id);
        // 如果是管理员的话
        if ("1".equals(String.valueOf(originUser.getIsAdministrator()))) {
            UserInfo operatorUser = userProvider.get();
            // 管理员可以修改自己，但是无法修改其他管理员
            if (operatorUser.getIsAdministrator()) {
                if (originUser.getEnabledMark() != 0 && entity.getEnabledMark() == 0) {
                    return ActionResult.fail("无法禁用管理员用户");
                }
                if (!"admin".equals(userService.getInfo(operatorUser.getUserId()).getAccount())) {
                    if (!operatorUser.getUserId().equals(id)) {
                        return ActionResult.fail("管理员只能修改自己，不能修改其他管理员");
                    }
                }
            } else {
                return ActionResult.fail("无法修改管理员账户");
            }
        }
        //直属主管不能是自己
        if (id.equals(userUpForm.getManagerId())) {
            return ActionResult.fail("直属主管不能是自己");
        }
        if (!originUser.getAccount().equals(entity.getAccount())) {
            if (userService.isExistByAccount(entity.getAccount())) {
                return ActionResult.fail("账户名称不能重复");
            }
        }
        // 验证是否有十级,验证是否是自己的下属
        boolean subordinate = userService.isSubordinate(id, userUpForm.getManagerId());
        if (subordinate) {
            return ActionResult.fail("直属主管不能是我的下属用户");
        }
        // 如果账号被锁定
        if ("2".equals(String.valueOf(entity.getEnabledMark()))) {
            entity.setUnlockTime(null);
            entity.setLogErrorCount(0);
        }
        // 如果原来是锁定，现在不锁定，则置空错误次数
        if (originUser.getEnabledMark() == 2 && entity.getEnabledMark() == 1) {
            entity.setUnlockTime(null);
            entity.setLogErrorCount(0);
        }
        boolean flag = userService.update(id, entity);
        threadPoolExecutor.execute(() -> {
            try {
                //修改用户之后判断是否需要同步到企业微信
                synThirdQyService.updateUserSysToQy(false, entity, "");
                //修改用户之后判断是否需要同步到钉钉
                synThirdDingTalkService.updateUserSysToDing(false, entity, "");
            } catch (Exception e) {
                log.error("修改用户之后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
            }
        });
        if (!flag) {
            return ActionResult.fail(MsgCode.FA002.get());
        }
        // 删除在线的用户
        userService.delCurUser(id);
        PullUserUtil.syncUser(entity, "update", userProvider.get().getTenantId());
        return ActionResult.success(MsgCode.SU004.get());
    }


    /**
     * 删除用户
     *
     * @param id 主键值
     * @return ignore
     */
    @UserPermission
    @Operation(summary = "删除用户")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.user")
    @DeleteMapping("/{id}")
    public ActionResult<String> delete(@PathVariable("id") String id) {
        UserEntity entity = userService.getInfo(id);
        if (entity != null) {
            if ("1".equals(String.valueOf(entity.getIsAdministrator()))) {
                return ActionResult.fail("无法删除管理员账户");
            }
            //判断是否是部门主管
            if (organizeService.getList().stream().filter(t -> id.equals(t.getManager())).collect(Collectors.toList()).size() > 0) {
                return ActionResult.fail("此用户为某部门主管，无法删除");
            }
            // 有下属不允许删除
            if (userService.getListByManagerId(id, null).size() > 0) {
                return ActionResult.fail("此用户有下属，无法删除");
            }
            String tenantId = StringUtil.isEmpty(userProvider.get().getTenantId()) ? "" : userProvider.get().getTenantId();
            String catchKey = tenantId + "allUser";
            if (redisUtil.exists(catchKey)) {
                redisUtil.remove(catchKey);
            }
            userService.delete(entity);
            threadPoolExecutor.execute(() -> {
                try {
                    //删除用户之后判断是否需要同步到企业微信
                    synThirdQyService.deleteUserSysToQy(false, id, "");
                    //删除用户之后判断是否需要同步到钉钉
                    synThirdDingTalkService.deleteUserSysToDing(false, id, "");
                } catch (Exception e) {
                    log.error("删除用户之后同步失败到企业微信或钉钉失败，异常：" + e.getMessage());
                }
            });
            userService.delCurUser(entity.getId());
            PullUserUtil.syncUser(entity, "delete", userProvider.get().getTenantId());
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.fail(MsgCode.FA003.get());
    }


    /**
     * 修改用户密码
     *
     * @param id 主键
     * @param userResetPasswordForm 修改密码模型
     * @return ignore
     */
    @UserPermission
    @Operation(summary = "修改用户密码")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true),
            @Parameter(name = "userResetPasswordForm", description = "修改密码模型", required = true)
    })
    @SaCheckPermission("permission.user")
    @PostMapping("/{id}/Actions/ResetPassword")
    public ActionResult<String> modifyPassword(@PathVariable("id") String id, @RequestBody @Valid UserResetPasswordForm userResetPasswordForm) {
        UserEntity entity = userService.getInfo(id);
        if (entity != null) {
            entity.setPassword(userResetPasswordForm.getUserPassword());
            userService.updatePassword(entity);
            userService.delCurUser(entity.getId());
            PullUserUtil.syncUser(entity, "modifyPassword", userProvider.get().getTenantId());
            return ActionResult.success(MsgCode.SU005.get());
        }
        return ActionResult.success("操作失败,用户不存在");
    }

    /**
     * 更新用户状态
     *
     * @param id 主键值
     * @return ignore
     */
    @Operation(summary = "更新用户状态")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.user")
    @PutMapping("/{id}/Actions/State")
    public ActionResult<String> disable(@PathVariable("id") String id) throws Exception {
        UserEntity entity = userService.getInfo(id);
        if (entity != null) {
            if ("1".equals(String.valueOf(entity.getIsAdministrator()))) {
                return ActionResult.fail("无法修改管理员账户状态");
            }
            if (entity.getEnabledMark() != null) {
                if ("1".equals(String.valueOf(entity.getEnabledMark()))) {
                    entity.setEnabledMark(0);
                    userService.delCurUser(entity.getId());
                    userService.update(id, entity);
                } else {
                    entity.setEnabledMark(1);
                    userService.update(id, entity);
                }
            } else {
                entity.setEnabledMark(1);
                userService.update(id, entity);
            }
            return ActionResult.success(MsgCode.SU005.get());
        }
        return ActionResult.success("操作失败,用户不存在");
    }

    /**
     * 解除锁定
     *
     * @param id 主键值
     * @return ignore
     */
    @Operation(summary = "解除锁定")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.user")
    @PutMapping("/{id}/Actions/unlock")
    public ActionResult<String> unlock(@PathVariable("id") String id) throws Exception {
        UserEntity entity = userService.getInfo(id);
        if (entity != null) {
            // 状态变成正常
            entity.setEnabledMark(1);
            entity.setUnlockTime(null);
            entity.setLogErrorCount(0);
            entity.setId(id);
            userService.updateById(entity);
            return ActionResult.success(MsgCode.SU005.get());
        }
        return ActionResult.success("操作失败,用户不存在");
    }

    /**
     * 获取用户基本信息
     *
     * @param userIdModel 用户id
     * @return ignore
     */
    @Operation(summary = "获取用户基本信息")
    @Parameters({
            @Parameter(name = "userIdModel", description = "用户id", required = true)
    })
    @PostMapping("/getUserList")
    public ActionResult<ListVO<UserIdListVo>> getUserList(@RequestBody UserIdModel userIdModel) {
        List<UserEntity> userName = userService.getUserName(userIdModel.getUserId(), true);
        List<UserIdListVo> list = JsonUtil.getJsonToList(userName, UserIdListVo.class);
        List<UserRelationEntity> listByUserIds = userRelationService.getRelationByUserIds(list.stream().map(UserIdListVo::getId).collect(Collectors.toList()));
        for (UserIdListVo entity : list) {
            if (entity == null) {
                break;
            }
            entity.setFullName(entity.getRealName() + "/" + entity.getAccount());
            List<UserRelationEntity> listByUserId = listByUserIds.stream().filter(t -> t.getUserId().equals(entity.getId())).collect(Collectors.toList());
            StringBuilder stringBuilder = new StringBuilder();
            List<OrganizeEntity> orgEntityList = organizeService.getOrgEntityList(listByUserId.stream().map(UserRelationEntity::getObjectId).collect(Collectors.toList()), false);
            listByUserId.forEach(t -> {
                OrganizeEntity organizeEntity = orgEntityList.stream().filter(org -> org.getId().equals(t.getObjectId())).findFirst().orElse(null);
                if (organizeEntity != null) {
                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                    if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                        stringBuilder.append("," + fullNameByOrgIdTree);
                    }
                }
            });
            if (stringBuilder.length() > 0) {
                entity.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
            }
            entity.setHeadIcon(UploaderUtil.uploaderImg(entity.getHeadIcon()));
        }
        ListVO<UserIdListVo> listVO = new ListVO<>();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }

    /**
     * 获取选中组织、岗位、角色、用户基本信息
     *
     * @param userIdModel 用户id
     * @return ignore
     */
    @Operation(summary = "获取选中组织、岗位、角色、用户基本信息")
    @Parameters({
            @Parameter(name = "userIdModel", description = "用户id", required = true)
    })
    @PostMapping("/getSelectedList")
    public ActionResult<ListVO<UserIdListVo>> getSelectedList(@RequestBody UserIdModel userIdModel) {
        List<UserIdListVo> list = new ArrayList<>();
        List<String> ids = userIdModel.getUserId();
        if (ids != null) {
            ids.forEach(selectedId -> {
                if (StringUtil.isNotEmpty(selectedId)) {
                    String[] split = selectedId.split("--");
                    // 截取type后获取详情
                    if (split.length > 1) {
                        String type = split[1];
                        if (PermissionConst.COMPANY.equalsIgnoreCase(type) || PermissionConst.DEPARTMENT.equalsIgnoreCase(type)) {
                            OrganizeEntity organizeEntity = organizeService.getInfo(split[0]);
                            if (organizeEntity != null) {
                                UserIdListVo vo = JsonUtil.getJsonToBean(organizeEntity, UserIdListVo.class);
                                if ("department".equals(organizeEntity.getCategory())) {
                                    vo.setIcon("icon-ym icon-ym-tree-department1");
                                } else if ("company".equals(organizeEntity.getCategory())) {
                                    vo.setIcon("icon-ym icon-ym-tree-organization3");
                                }
                                vo.setOrganize(organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/"));
                                vo.setOrganizeIds(organizeService.getOrgIdTree(organizeEntity));
                                vo.setType(organizeEntity.getCategory());
                                list.add(vo);
                            }
                        } else if (PermissionConst.ROLE.equalsIgnoreCase(type)) {
                            RoleEntity roleEntity = roleService.getInfo(split[0]);
                            if (roleEntity != null) {
                                UserIdListVo vo = JsonUtil.getJsonToBean(roleEntity, UserIdListVo.class);
                                // 获取角色的所属组织
                                List<OrganizeRelationEntity> relationListByRoleId = organizeRelationService.getRelationListByRoleId(vo.getId());
                                StringJoiner orgName = new StringJoiner(",");
                                relationListByRoleId.forEach(organizeRelationEntity -> {
                                    String organizeId = organizeRelationEntity.getOrganizeId();
                                    OrganizeEntity entity = organizeService.getInfo(organizeId);
                                    if (entity != null) {
                                        String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(entity.getOrganizeIdTree(), "/");
                                        orgName.add(fullNameByOrgIdTree);
                                    }
                                });
                                vo.setOrganize(orgName.toString());
                                vo.setType("role");
                                vo.setIcon("icon-ym icon-ym-generator-role");
                                list.add(vo);
                            }
                        } else if (PermissionConst.POSITION.equalsIgnoreCase(type)) {
                            PositionEntity positionEntity = positionService.getInfo(split[0]);
                            if (positionEntity != null) {
                                UserIdListVo vo = JsonUtil.getJsonToBean(positionEntity, UserIdListVo.class);
                                // 获取角色的所属组织
                                List<OrganizeRelationEntity> relationListByRoleId = organizeRelationService.getRelationListByObjectIdAndType(PermissionConst.POSITION, vo.getId());
                                StringJoiner orgName = new StringJoiner(",");
                                relationListByRoleId.forEach(organizeRelationEntity -> {
                                    String organizeId = organizeRelationEntity.getOrganizeId();
                                    OrganizeEntity entity = organizeService.getInfo(organizeId);
                                    if (entity != null) {
                                        String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(entity.getOrganizeIdTree(), "/");
                                        orgName.add(fullNameByOrgIdTree);
                                    }
                                });
                                vo.setOrganize(orgName.toString());
                                vo.setType("position");
                                vo.setIcon("icon-ym icon-ym-tree-position1");
                                list.add(vo);
                            }
                        } else if (PermissionConst.GROUP.equalsIgnoreCase(type)) {
                            GroupEntity groupEntity = groupService.getInfo(split[0]);
                            if (groupEntity != null) {
                                UserIdListVo vo = JsonUtil.getJsonToBean(groupEntity, UserIdListVo.class);
                                vo.setIcon("icon-ym icon-ym-generator-group1");
                                vo.setType("group");
                                list.add(vo);
                            }
                        } else if ("user".equalsIgnoreCase(type)) {
                            UserEntity userEntity = userService.getInfo(split[0]);
                            if (userEntity != null) {
                                UserIdListVo vo = JsonUtil.getJsonToBean(userEntity, UserIdListVo.class);
                                List<UserRelationEntity> listByObjectType = userRelationService.getListByObjectType(userEntity.getId(), PermissionConst.ORGANIZE);
                                StringJoiner orgName = new StringJoiner(",");
                                listByObjectType.forEach(userRelationEntity -> {
                                    OrganizeEntity info = organizeService.getInfo(userRelationEntity.getObjectId());
                                    if (info != null) {
                                        String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(info.getOrganizeIdTree(), "/");
                                        orgName.add(fullNameByOrgIdTree);
                                    }
                                });
                                vo.setOrganize(orgName.toString());
                                vo.setType("user");
                                vo.setHeadIcon(UploaderUtil.uploaderImg(vo.getHeadIcon()));
                                vo.setFullName(vo.getRealName() + "/" + vo.getAccount());
                                list.add(vo);
                            }
                        }
                    } else {
                        UserEntity userEntity = userService.getInfo(split[0]);
                        if (userEntity != null) {
                            UserIdListVo vo = JsonUtil.getJsonToBean(userEntity, UserIdListVo.class);
                            List<UserRelationEntity> listByObjectType = userRelationService.getListByObjectType(userEntity.getId(), PermissionConst.ORGANIZE);
                            StringJoiner orgName = new StringJoiner(",");
                            listByObjectType.forEach(userRelationEntity -> {
                                OrganizeEntity info = organizeService.getInfo(userRelationEntity.getObjectId());
                                if (info != null) {
                                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(info.getOrganizeIdTree(), "/");
                                    orgName.add(fullNameByOrgIdTree);
                                }
                            });
                            vo.setOrganize(orgName.toString());
                            vo.setType("user");
                            vo.setHeadIcon(UploaderUtil.uploaderImg(vo.getHeadIcon()));
                            vo.setFullName(vo.getRealName() + "/" + vo.getAccount());
                            list.add(vo);
                        }
                    }
                }
            });
        }
        ListVO<UserIdListVo> listVO = new ListVO<>();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }



    /**
     * 获取用户基本信息
     *
     * @param userIdModel 用户id
     * @return ignore
     */
    @Operation(summary = "获取选中用户基本信息")
    @Parameters({
            @Parameter(name = "userIdModel", description = "用户id", required = true)
    })
    @PostMapping("/getSelectedUserList")
    public ActionResult<PageListVO<UserIdListVo>> getSelectedUserList(@RequestBody UserIdModelByPage userIdModel) {
        List<String> ids = userIdModel.getUserId();
        List<String> userRelationEntities = userService.getUserIdList(ids);
        // 得到所有的用户id关系
        List<UserEntity> userName = userService.getUserNames(userRelationEntities, userIdModel.getPagination(), false, true);
        List<UserIdListVo> jsonToList = JsonUtil.getJsonToList(userName, UserIdListVo.class);
        jsonToList.forEach(userIdListVo -> {
            List<UserRelationEntity> listByObjectType = userRelationService.getListByObjectType(userIdListVo.getId(), PermissionConst.ORGANIZE);
            StringJoiner orgName = new StringJoiner(",");
            listByObjectType.forEach(userRelationEntity -> {
                OrganizeEntity info = organizeService.getInfo(userRelationEntity.getObjectId());
                if (info != null) {
                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(info.getOrganizeIdTree(), "/");
                    orgName.add(fullNameByOrgIdTree);
                }
            });
            userIdListVo.setOrganize(orgName.toString());
            userIdListVo.setType("user");

            userIdListVo.setFullName(userIdListVo.getRealName() + "/" + userIdListVo.getAccount());
            userIdListVo.setHeadIcon(UploaderUtil.uploaderImg(userIdListVo.getHeadIcon()));
        });
        PaginationVO paginationVO = JsonUtil.getJsonToBean(userIdModel.getPagination(), PaginationVO.class);
        return ActionResult.page(jsonToList, paginationVO);
    }

    /**
     * 获取组织下的人员
     *
     * @param page 页面信息
     * @return ignore
     */
    @Operation(summary = "获取组织下的人员")
    @GetMapping("/getOrganization")
    public ActionResult<List<UserIdListVo>> getOrganization(PageUser page) {
        String departmentId = page.getOrganizeId();
        // 判断是否获取当前组织下的人员
        if ("0".equals(departmentId)) {
            departmentId = userProvider.get().getDepartmentId();
            // 为空则取组织id
            if (StringUtil.isEmpty(departmentId)) {
                departmentId = userProvider.get().getOrganizeId();
            }
        }
        List<UserEntity> list = userService.getListByOrganizeId(departmentId, page.getKeyword());
        List<UserIdListVo> jsonToList = JsonUtil.getJsonToList(list, UserIdListVo.class);
        jsonToList.forEach(t -> {
            t.setRealName(t.getRealName() + "/" + t.getAccount());
            t.setFullName(t.getRealName());
            List<UserRelationEntity> listByUserId = userRelationService.getListByUserId(t.getId()).stream().filter(ur -> ur != null && PermissionConst.ORGANIZE.equals(ur.getObjectType())).collect(Collectors.toList());
            StringBuilder stringBuilder = new StringBuilder();
            listByUserId.forEach(tt -> {
                OrganizeEntity organizeEntity = organizeService.getInfo(tt.getObjectId());
                if (organizeEntity != null) {
                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                    if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                        stringBuilder.append("," + fullNameByOrgIdTree);
                    }
                }
            });
            if (stringBuilder.length() > 0) {
                t.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
            }
            t.setHeadIcon(UploaderUtil.uploaderImg(t.getHeadIcon()));
        });
        return ActionResult.success(jsonToList);
    }

    /**
     * 获取岗位人员
     *
     * @param page 页面信息
     * @return ignore
     */
    @Operation(summary = "获取岗位人员")
    @GetMapping("/GetUsersByPositionId")
    public ActionResult<List<UserByRoleVO>> getUsersByPositionId(UsersByPositionModel page) {
        List<UserByRoleVO> list = new ArrayList<>(1);
        String keyword = page.getKeyword();
        // 岗位id
        String positionId = page.getPositionId();
        // 得到关联的组织id
        List<OrganizeRelationEntity> relationListByObjectIdAndType = organizeRelationService.getRelationListByObjectIdAndType(PermissionConst.POSITION, positionId);
        if (relationListByObjectIdAndType.size() == 1) {
            UserByRoleVO vo = new UserByRoleVO();
            String organizeId = relationListByObjectIdAndType.get(0).getOrganizeId();
            // 得到组织信息
            OrganizeEntity organizeEntity = organizeService.getInfo(organizeId);
            if (Objects.nonNull(organizeEntity)) {
                vo.setId(organizeEntity.getId());
                vo.setType(organizeEntity.getCategory());
                if ("department".equals(organizeEntity.getCategory())) {
                    vo.setIcon("icon-ym icon-ym-tree-department1");
                } else {
                    vo.setIcon("icon-ym icon-ym-tree-organization3");
                }
                vo.setEnabledMark(organizeEntity.getEnabledMark());
                // 组装组织名称
                String orgName = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                vo.setFullName(orgName);
                // 赋予子集
                List<UserByRoleVO> userByRoleVOS = new ArrayList<>(16);
                List<UserEntity> lists = userService.getListByOrganizeId(organizeEntity.getId(), keyword);
                if (lists.size() > 0) {
                    vo.setHasChildren(true);
                    vo.setIsLeaf(false);
                    lists.stream().forEach(t->{
                        UserByRoleVO userByRoleVO = new UserByRoleVO();
                        userByRoleVO.setParentId(organizeEntity.getId());
                        userByRoleVO.setId(t.getId());
                        userByRoleVO.setFullName(t.getRealName() + "/" + t.getAccount());
                        userByRoleVO.setEnabledMark(t.getEnabledMark());
                        userByRoleVO.setHeadIcon(UploaderUtil.uploaderImg(t.getHeadIcon()));
                        userByRoleVO.setOrganize(organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/"));
                        userByRoleVO.setIsLeaf(true);
                        userByRoleVO.setHasChildren(false);
                        userByRoleVO.setIcon("icon-ym icon-ym-tree-user2");
                        userByRoleVO.setType("user");
                        userByRoleVOS.add(userByRoleVO);
                    });
                    vo.setChildren(userByRoleVOS);
                } else {
                    vo.setHasChildren(false);
                    vo.setIsLeaf(true);
                    vo.setChildren(new ArrayList<>());
                }
                list.add(vo);
            }
        }
        return ActionResult.success(list);
    }

    /**
     * 角色成员弹窗
     *
     * @param model 页面信息
     * @return ignore
     */
    @Operation(summary = "角色成员弹窗")
    @SaCheckPermission("permission.role")
    @GetMapping("/GetUsersByRoleOrgId")
    public ActionResult<List<UserByRoleVO>> getUsersByRoleOrgId(UserByRoleModel model) {
        List<UserByRoleVO> jsonToList = new ArrayList<>(16);
        // 得到组织关系
        List<OrganizeRelationEntity> relationListByRoleId = organizeRelationService.getRelationListByRoleId(model.getRoleId());
        // 得到组织信息
        List<OrganizeEntity> orgEntityList = organizeService.getOrgEntityList(relationListByRoleId.stream().map(OrganizeRelationEntity::getOrganizeId).collect(Collectors.toList()), true);
        //判断是否搜索关键字
        if (StringUtil.isNotEmpty(model.getKeyword())) {
            //通过关键字查询
            List<UserEntity> list = userService.getList(orgEntityList.stream().map(OrganizeEntity::getId).collect(Collectors.toList()), model.getKeyword());
            List<UserRelationEntity> listByUserIds = userRelationService.getRelationByUserIds(list.stream().map(UserEntity::getId).collect(Collectors.toList()));
            //遍历用户给要返回的值插入值
            for (UserEntity entity : list) {
                UserByRoleVO vo = new UserByRoleVO();
                vo.setId(entity.getId());
                vo.setFullName(entity.getRealName() + "/" + entity.getAccount());
                vo.setEnabledMark(entity.getEnabledMark());
                vo.setIsLeaf(true);
                vo.setHeadIcon(UploaderUtil.uploaderImg(entity.getHeadIcon()));
                List<UserRelationEntity> listByUserId = listByUserIds.stream().filter(t -> t.getUserId().equals(entity.getId())).collect(Collectors.toList());
                StringBuilder stringBuilder = new StringBuilder();
                List<OrganizeEntity> orgEntityLists = organizeService.getOrgEntityList(listByUserId.stream().map(UserRelationEntity::getObjectId).collect(Collectors.toList()), false);
                listByUserId.forEach(t -> {
                    OrganizeEntity orgEntity = orgEntityLists.stream().filter(org -> org.getId().equals(t.getObjectId())).findFirst().orElse(null);
                    if (orgEntity != null) {
                        String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(orgEntity.getOrganizeIdTree(), "/");
                        if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                            stringBuilder.append("," + fullNameByOrgIdTree);
                        }
                    }
                });
                if (stringBuilder.length() > 0) {
                    vo.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
                }
                vo.setHasChildren(false);
                vo.setIcon("icon-ym icon-ym-tree-user2");
                vo.setType("user");
                jsonToList.add(vo);
            }
            return ActionResult.success(jsonToList);
        }
        //获取所有组织
        List<OrganizeEntity> collect = organizeService.getList().stream().filter(t -> t.getEnabledMark() == 1).collect(Collectors.toList());
        //判断时候传入组织id
        //如果传入组织id，则取出对应的子集
        if (!"0".equals(model.getOrganizeId())) {
            //通过组织查询部门及人员
            //单个组织
            List<OrganizeEntity> list = collect.stream().filter(t -> model.getOrganizeId().equals(t.getId())).collect(Collectors.toList());
            if (list.size() > 0) {
                //获取组织信息
                OrganizeEntity organizeEntity = list.get(0);
                //取出组织下的部门
                List<OrganizeEntity> collect1 = collect.stream().filter(t -> t.getParentId().equals(organizeEntity.getId())).collect(Collectors.toList());
                // 判断组织关系中是否有子部门id
                List<OrganizeEntity> organizeEntities = new ArrayList<>();
                for (OrganizeEntity entity : collect1) {
                    List<OrganizeRelationEntity> collect2 = relationListByRoleId.stream().filter(t -> entity.getId().equals(t.getOrganizeId())).collect(Collectors.toList());
                    collect2.stream().forEach(t->{
                        if (StringUtil.isNotEmpty(t.getOrganizeId())) {
                            organizeEntities.add(organizeService.getInfo(t.getOrganizeId()));
                        }
                    });
                }
                // 其他不是子集的直接显示
                List<OrganizeRelationEntity> collect2 = relationListByRoleId.stream()
                        .filter(item -> !organizeEntities.stream().map(e -> e.getId())
                                .collect(Collectors.toList()).contains(item.getOrganizeId()))
                        .collect(Collectors.toList());
                // 移除掉上级不是同一个的
                List<OrganizeRelationEntity> collect3 = collect2.stream().filter(t -> !organizeService.getInfo(t.getOrganizeId()).getOrganizeIdTree().contains(model.getOrganizeId())).collect(Collectors.toList());
                collect2.removeAll(collect3);
                List<OrganizeRelationEntity> collect4 = collect2.stream().filter(t -> !t.getOrganizeId().equals(model.getOrganizeId())).collect(Collectors.toList());
                List<OrganizeEntity> collect5 = collect.stream().filter(x -> collect4.stream().map(d -> d.getOrganizeId()).collect(Collectors.toList()).contains(x.getId())).collect(Collectors.toList());
                List<OrganizeEntity> organizeEntities1 = new ArrayList<>(collect5);
                // 不是子集的对比子集的
                for (OrganizeEntity entity : collect5) {
                    for (OrganizeEntity organizeEntity1 : organizeEntities) {
                        if (entity.getOrganizeIdTree().contains(organizeEntity1.getId())) {
                            organizeEntities1.remove(entity);
                        }
                    }
                }

                //取出组织下的人员
                List<UserEntity> entityList = userService.getListByOrganizeId(model.getOrganizeId(), null);
                List<UserRelationEntity> listByUserIds = userRelationService.getRelationByUserIds(entityList.stream().map(UserEntity::getId).collect(Collectors.toList()));
                for (UserEntity entity : entityList) {
                    UserByRoleVO vo = new UserByRoleVO();
                    vo.setId(entity.getId());
                    vo.setFullName(entity.getRealName() + "/" + entity.getAccount());
                    vo.setEnabledMark(entity.getEnabledMark());
                    vo.setIsLeaf(true);
                    vo.setHasChildren(false);
                    vo.setIcon("icon-ym icon-ym-tree-user2");
                    vo.setType("user");
                    vo.setHeadIcon(UploaderUtil.uploaderImg(entity.getHeadIcon()));
                    List<UserRelationEntity> listByUserId = listByUserIds.stream().filter(t -> t.getUserId().equals(entity.getId())).collect(Collectors.toList());
                    StringBuilder stringBuilder = new StringBuilder();
                    List<OrganizeEntity> orgEntityLists = organizeService.getOrgEntityList(listByUserId.stream().map(UserRelationEntity::getObjectId).collect(Collectors.toList()), false);
                    listByUserId.forEach(t -> {
                        OrganizeEntity orgEntity = orgEntityLists.stream().filter(org -> org.getId().equals(t.getObjectId())).findFirst().orElse(null);
                        if (orgEntity != null) {
                            String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(orgEntity.getOrganizeIdTree(), "/");
                            if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                                stringBuilder.append("," + fullNameByOrgIdTree);
                            }
                        }
                    });
                    if (stringBuilder.length() > 0) {
                        vo.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
                    }
                    jsonToList.add(vo);
                }
                // 处理子集断层
                // 断层拥有同一层的话，需要合并
                List<OrganizeEntity> organizeEntities2 = new ArrayList<>(collect5);
                for (OrganizeEntity entity : organizeEntities2) {
                    List<OrganizeEntity> collect6 = organizeEntities2.stream().filter(t -> !entity.getId().equals(t.getId()) && t.getOrganizeIdTree().contains(entity.getOrganizeIdTree())).collect(Collectors.toList());
                    organizeEntities1.removeAll(collect6);
                }

                for (OrganizeEntity entity : organizeEntities1) {
                    StringBuilder stringBuffer = new StringBuilder();
                    String[] split = entity.getOrganizeIdTree().split(",");
                    List<String> list1 = Arrays.asList(split);
                    List<String> list2 = new ArrayList<>(list1);
                    int indexOf = list2.indexOf(model.getOrganizeId());
                    while (indexOf >= 0) {
                        list2.remove(indexOf);
                        indexOf--;
                    }
                    StringBuilder organizeIdTree = new StringBuilder();
                    for (String parentId : list2) {
                        OrganizeEntity organizes = organizeService.getInfo(parentId);
                        if (Objects.nonNull(organizes) && StringUtil.isNotEmpty(organizes.getFullName())) {
                            organizeIdTree.append("/" + organizes.getFullName());
                        }
                    }
                    String toString = organizeIdTree.toString();
                    if (StringUtil.isNotEmpty(toString)) {
                        String organizeId = toString.replaceFirst("/", "");
                        stringBuffer.append("," + organizeId);
                    }
                    UserByRoleVO userByRoleVO = new UserByRoleVO();
                    userByRoleVO.setId(entity.getId());
                    userByRoleVO.setType(entity.getCategory());
                    userByRoleVO.setFullName(stringBuffer.toString().replace(",", ""));
                    if ("department".equals(entity.getCategory())) {
                        userByRoleVO.setIcon("icon-ym icon-ym-tree-department1");
                    } else {
                        userByRoleVO.setIcon("icon-ym icon-ym-tree-organization3");
                    }
                    userByRoleVO.setHasChildren(true);
                    userByRoleVO.setIsLeaf(false);
                    userByRoleVO.setEnabledMark(entity.getEnabledMark());
                    jsonToList.add(userByRoleVO);
                }
                for (OrganizeEntity entitys : organizeEntities) {
                    UserByRoleVO vo = new UserByRoleVO();
                    vo.setId(entitys.getId());
                    vo.setType(entitys.getCategory());
                    vo.setFullName(entitys.getFullName());
                    if ("department".equals(entitys.getCategory())) {
                        vo.setIcon("icon-ym icon-ym-tree-department1");
                    } else {
                        vo.setIcon("icon-ym icon-ym-tree-organization3");
                    }
                    vo.setHasChildren(true);
                    vo.setIsLeaf(false);
                    vo.setEnabledMark(entitys.getEnabledMark());
                    jsonToList.add(vo);
                }
            }
            return ActionResult.success(jsonToList);
        }

        // 判断是否有父级
        Set<OrganizeEntity> set = new HashSet<>(16);
        for (OrganizeEntity entity : orgEntityList) {
            List<OrganizeEntity> collect1 = orgEntityList.stream().filter(t -> !entity.getId().equals(t.getId()) && entity.getOrganizeIdTree().contains(t.getOrganizeIdTree())).collect(Collectors.toList());
            set.addAll(collect1);
        }
        List<OrganizeEntity> list = new ArrayList<>(set);
        // 从list中一处已经有的
        List<OrganizeEntity> list1 = new ArrayList<>(list);
        for (OrganizeEntity organizeEntity : list) {
            List<OrganizeEntity> collect1 = list.stream().filter(t -> !organizeEntity.getId().equals(t.getId()) && t.getOrganizeIdTree().contains(organizeEntity.getId())).collect(Collectors.toList());
            list1.removeAll(collect1);
        }
        list = list1;
        // 纯断层的
        List<OrganizeEntity> list2 = new ArrayList<>(orgEntityList);
        for (OrganizeEntity organizeEntity: orgEntityList){
            if (list.stream().filter(t -> organizeEntity.getOrganizeIdTree().contains(t.getId())).count() > 0) {
                list2.remove(organizeEntity);
            }
        }
        list.addAll(list2);
        for (OrganizeEntity organizeEntity : list) {
            if (organizeEntity != null && organizeEntity.getEnabledMark() == 1) {
                UserByRoleVO userByRoleVO = new UserByRoleVO();
                userByRoleVO.setId(organizeEntity.getId());
                userByRoleVO.setType(organizeEntity.getCategory());
                String orgName = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                userByRoleVO.setFullName(orgName);
                if ("department".equals(organizeEntity.getCategory())) {
                    userByRoleVO.setIcon("icon-ym icon-ym-tree-department1");
                } else {
                    userByRoleVO.setIcon("icon-ym icon-ym-tree-organization3");
                }
                userByRoleVO.setHasChildren(true);
                userByRoleVO.setIsLeaf(false);
                userByRoleVO.setEnabledMark(organizeEntity.getEnabledMark());
                jsonToList.add(userByRoleVO);
            }
            }
        return ActionResult.success(jsonToList);
    }

    /**
     * 获取我的下属(不取子集)
     *
     * @param page 页面信息
     * @return ignore
     */
    @Operation(summary = "获取我的下属(不取子集)")
    @Parameters({
            @Parameter(name = "page", description = "关键字", required = true)
    })
    @PostMapping("/getSubordinates")
    public ActionResult<List<UserIdListVo>> getSubordinates(@RequestBody Page page) {
        List<UserEntity> list = userService.getListByManagerId(userProvider.get().getUserId(), page.getKeyword());
        List<UserIdListVo> jsonToList = JsonUtil.getJsonToList(list, UserIdListVo.class);
        jsonToList.forEach(t -> {
            t.setRealName(t.getRealName() + "/" + t.getAccount());
            t.setFullName(t.getRealName());
            List<UserRelationEntity> listByUserId = userRelationService.getListByUserId(t.getId()).stream().filter(ur -> t != null && PermissionConst.ORGANIZE.equals(ur.getObjectType())).collect(Collectors.toList());
            StringBuilder stringBuilder = new StringBuilder();
            listByUserId.forEach(tt -> {
                OrganizeEntity organizeEntity = organizeService.getInfo(tt.getObjectId());
                if (organizeEntity != null) {
                    String fullNameByOrgIdTree = organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/");
                    if (StringUtil.isNotEmpty(fullNameByOrgIdTree)) {
                        stringBuilder.append("," + fullNameByOrgIdTree);
                    }
                }
            });
            if (stringBuilder.length() > 0) {
                t.setOrganize(stringBuilder.toString().replaceFirst(",", ""));
            }
        });
        return ActionResult.success(jsonToList);
    }

    /**
     * 导出excel
     *
     * @param dataType   导出方式
     * @param selectKey 选择列
     * @param pagination 分页
     * @return ignore
     */
    @Operation(summary = "导出excel")
    @SaCheckPermission("permission.user")
    @GetMapping("/ExportExcel")
    public ActionResult<DownloadVO> Export(String dataType, String selectKey, PaginationUser pagination) {
        // 导出
        DownloadVO vo = userService.exportExcel(dataType, selectKey, pagination);
        return ActionResult.success(vo);
    }

    /**
     * 模板下载
     *
     * @return ignore
     */
    @Operation(summary = "模板下载")
    @SaCheckPermission("permission.user")
    @GetMapping("/TemplateDownload")
    public ActionResult<DownloadVO> TemplateDownload() {
        UserInfo userInfo = userProvider.get();
        DownloadVO vo = DownloadVO.builder().build();
        try {
            vo.setName("用户信息.xlsx");
            vo.setUrl(UploaderUtil.uploaderFile("/api/file/DownloadModel?encryption=", "用户信息" +
                    ".xlsx" + "#" + "Temporary"));
        } catch (Exception e) {
            log.error("信息导出Excel错误:" + e.getMessage());
        }
        return ActionResult.success(vo);
    }


    /**
     * 导入数据
     *
     * @param data 导入模型
     * @return ignore
     */
    @Operation(summary = "导入数据")
    @Parameters({
            @Parameter(name = "data", description = "导入模型", required = true)
    })
    @SaCheckPermission("permission.user")
    @PostMapping("/ImportData")
    public ActionResult<UserImportVO> ImportData(@RequestBody UserExportVO data) {
        List<UserExportVO> dataList = JsonUtil.getJsonToList(data.getList(), UserExportVO.class);
        //导入数据
        UserImportVO result = userService.importData(dataList);
        return ActionResult.success(result);
    }

    /**
     * 导出错误报告
     *
     * @param data 导出模型
     * @return ignore
     */
    @Operation(summary = "导出错误报告")
    @Parameters({
            @Parameter(name = "data", description = "导出模型", required = true)
    })
    @SaCheckPermission("permission.user")
    @PostMapping("/ExportExceptionData")
    public ActionResult<DownloadVO> exportExceptionData(@RequestBody UserExportVO data) {
        List<UserExportVO> dataList = JsonUtil.getJsonToList(data.getList(), UserExportVO.class);
        //生成Excel
        DownloadVO vo = userService.exportExceptionData(dataList);
        return ActionResult.success(vo);
    }

    /*= different =*/

    /**
     * 上传文件(excel)
     *
     * @return ignore
     */
    @Operation(summary = "上传文件")
    @SaCheckPermission("permission.user")
    @PostMapping("/Uploader")
    public ActionResult<Object> Uploader() {
        List<MultipartFile> list = UpUtil.getFileAll();
        MultipartFile file = list.get(0);
        if (file.getOriginalFilename().endsWith(".xlsx") || file.getOriginalFilename().endsWith(".xls")) {
            String filePath = XSSEscape.escape(configValueUtil.getTemporaryFilePath());
            String fileName = XSSEscape.escape(RandomUtil.uuId() + "." + UpUtil.getFileType(file));
            // 上传文件
            FileInfo fileInfo = FileUploadUtils.uploadFile(file, filePath, fileName);
            DownloadVO vo = DownloadVO.builder().build();
            vo.setName(fileInfo.getFilename());
            return ActionResult.success(vo);
        } else {
            return ActionResult.fail("选择文件不符合导入");
        }
    }

    /**
     * 导入预览
     *
     * @param fileName 文件名
     * @return
     */
    @Operation(summary = "导入预览")
    @SaCheckPermission("permission.user")
    @GetMapping("/ImportPreview")
    public ActionResult<Map<String, Object>> ImportPreview(String fileName) throws ImportException {
        Map<String, Object> map = new HashMap<>();
        try {
            String filePath = configValueUtil.getTemporaryFilePath();
            @Cleanup InputStream inputStream = new ByteArrayInputStream(FileUploadUtils.downloadFileByte(filePath, fileName, false));
            // 得到数据
            List<UserExportVO> personList = ExcelUtil.importExcelByInputStream(inputStream, 0, 1, UserExportVO.class);
            // 预览数据
            map = userService.importPreview(personList);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ImportException(e.getMessage());
        }
        return ActionResult.success(map);
    }

    /**
     * 根据角色ID获取所属组织的所有成员
     *
     * @param pagination 分页模型
     * @return
     */
    @Operation(summary = "根据角色ID获取所有成员")
    @SaCheckPermission("permission.role")
    @GetMapping("/getUsersByRoleId")
    public ActionResult getUsersByRoleId(PaginationUser pagination) {
        List<UserEntity> userList = new ArrayList<>();
        if (roleService.getInfo(pagination.getRoleId()).getGlobalMark() == 1) {
            userList.addAll(userService.getList(pagination, null, false, false));
        } else {
            // 根据roleId获取所有组织
            userService.getListByRoleId(pagination.getRoleId()).forEach(u -> {
                userList.add(userService.getInfo(u.getId()));
            });
        }
        // 去重
        List<UserEntity> afterUserList = userList.stream().distinct().collect(Collectors.toList());
        if (StringUtil.isNotEmpty(pagination.getKeyword())) {
            afterUserList = afterUserList.stream().filter(t -> t.getRealName().contains(pagination.getKeyword()) || t.getAccount().contains(pagination.getKeyword())).collect(Collectors.toList());
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(pagination, PaginationVO.class);
        return ActionResult.page(afterUserList, paginationVO);
    }

    /**
     * 获取默认当前值用户ID
     *
     * @param userConditionModel 参数
     * @return 执行结构
     * @throws DataException ignore
     */
    @Operation(summary = "获取默认当前值用户ID")
    @Parameters({
            @Parameter(name = "userConditionModel", description = "参数", required = true)
    })
    @PostMapping("/getDefaultCurrentValueUserId")
    public ActionResult<?> getDefaultCurrentValueUserId(@RequestBody UserConditionModel userConditionModel) throws DataException {
        String userId = userService.getDefaultCurrentValueUserId(userConditionModel);
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("userId", userId);
        return ActionResult.success("查询成功", dataMap);
    }

}
