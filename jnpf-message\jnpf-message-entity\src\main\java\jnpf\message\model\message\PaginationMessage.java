package jnpf.message.model.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jnpf.base.Pagination;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/12 15:31
 */
@Data
public class PaginationMessage extends Pagination {
    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 是否已读
     */
    @Schema(description = "是否已读")
    private Integer isRead;

}
