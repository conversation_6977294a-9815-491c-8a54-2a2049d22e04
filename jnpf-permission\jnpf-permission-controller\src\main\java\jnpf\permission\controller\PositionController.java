package jnpf.permission.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jnpf.base.controller.SuperController;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jnpf.annotation.PositionPermission;
import jnpf.base.ActionResult;
import jnpf.base.vo.PageListVO;
import jnpf.base.vo.PaginationVO;
import jnpf.base.service.DictionaryDataService;
import jnpf.base.vo.ListVO;
import jnpf.base.entity.DictionaryDataEntity;
import jnpf.constant.MsgCode;
import jnpf.permission.constant.PermissionConst;
import jnpf.permission.entity.*;
import jnpf.exception.DataException;
import jnpf.permission.model.organize.OrganizeModel;
import jnpf.permission.model.permission.PermissionModel;
import jnpf.permission.model.position.*;
import jnpf.permission.model.role.RoleModel;
import jnpf.permission.model.role.RoleSelectorVO;
import jnpf.permission.model.user.mod.UserConditionModel;
import jnpf.permission.model.user.vo.UserIdListVo;
import jnpf.permission.service.*;
import jnpf.permission.util.PermissionUtil;
import jnpf.util.*;
import jnpf.util.treeutil.ListToTreeUtil;
import jnpf.util.treeutil.SumTree;
import jnpf.util.treeutil.newtreeutil.TreeDotUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 岗位信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Tag(name = "岗位管理", description = "Position")
@RestController
@RequestMapping("/api/permission/Position")
public class PositionController extends SuperController<PositionService, PositionEntity> {
    @Autowired
    private UserRelationService userRelationService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserProvider userProvider;
    @Autowired
    private PositionService positionService;
    @Autowired
    private OrganizeService organizeService;
    @Autowired
    private DictionaryDataService dictionaryDataService;
    @Autowired
    private OrganizeRelationService organizeRelationService;

    /**
     * 获取岗位管理信息列表
     *
     * @param paginationPosition 分页模型
     * @return
     */
    @Operation(summary = "获取岗位列表（分页）")
    @SaCheckPermission("permission.position")
    @GetMapping
    public ActionResult<PageListVO<PositionListVO>> list(PaginationPosition paginationPosition) {
        List<OrganizeEntity> list = organizeService.getList().stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<PositionEntity> data = positionService.getList(paginationPosition);
        //添加部门信息，部门映射到organizeId
        List<PositionListVO> voList = JsonUtil.getJsonToList(data, PositionListVO.class);
        //添加部门信息
        for (PositionListVO entity1 : voList) {
            OrganizeEntity entity = list.stream().filter(t -> t.getId().equals(entity1.getOrganizeId())).findFirst().orElse(new OrganizeEntity());
            if (entity1.getOrganizeId().equals(entity.getId())) {
                entity1.setDepartment(entity.getFullName());
            }
            entity1.setDepartment(PermissionUtil.getLinkInfoByOrgId(entity1.getOrganizeId(), organizeService, false));
        }
        //将type成中文名
        List<DictionaryDataEntity> dictionaryDataEntities= dictionaryDataService.getList("dae93f2fd7cd4df999d32f8750fa6a1e", false);
        for (PositionListVO entity1 : voList) {
            DictionaryDataEntity entity = dictionaryDataEntities.stream().filter(t -> t.getEnCode().equals(entity1.getType())).findFirst().orElse(null);
            if (entity != null) {
                entity1.setType(entity.getFullName());
            }
        }
        PaginationVO paginationVO = JsonUtil.getJsonToBean(paginationPosition, PaginationVO.class);
        return ActionResult.page(voList, paginationVO);
    }

    /**
     * 列表
     *
     * @return
     */
    @Operation(summary = "列表")
    @GetMapping("/All")
    public ActionResult<ListVO<PositionListAllVO>> listAll() {
        List<PositionEntity> list = positionService.getList().stream().filter(
                t -> "1".equals(String.valueOf(t.getEnabledMark()))
        ).collect(Collectors.toList());
        List<PositionListAllVO> vos = JsonUtil.getJsonToList(list, PositionListAllVO.class);
        ListVO<PositionListAllVO> vo = new ListVO<>();
        vo.setList(vos);
        return ActionResult.success(vo);
    }

    /**
     * 树形（机构+岗位）
     *
     * @return
     */
    @Operation(summary = "获取岗位下拉列表（公司+部门+岗位）")
    @GetMapping("/Selector")
    public ActionResult<ListVO<PositionSelectorVO>> selector() {
        List<PositionEntity> list1 = positionService.getList();
        List<OrganizeEntity> list2 = organizeService.getList();
        List<PosOrgModel> posList = new ArrayList<>();
        for (PositionEntity entity : list1) {
            PosOrgModel posOrgModel = JsonUtil.getJsonToBean(entity, PosOrgModel.class);
            String organizeId = entity.getOrganizeId();
            posOrgModel.setParentId(organizeId);
            posOrgModel.setType("position");
            posOrgModel.setIcon("icon-ym icon-ym-tree-position1");
            OrganizeEntity organizeEntity = organizeService.getInfo(organizeId);
            if (organizeEntity != null) {
                posOrgModel.setOrganize(organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/"));
                posOrgModel.setOrganizeIds(organizeService.getOrgIdTree(organizeEntity));
            } else {
                posOrgModel.setOrganizeIds(new ArrayList<>());
            }
            posList.add(posOrgModel);
        }
        List<PosOrgModel> orgList = JsonUtil.getJsonToList(list2, PosOrgModel.class);
        for (PosOrgModel entity1 : orgList) {
            if ("department".equals(entity1.getType())) {
                entity1.setIcon("icon-ym icon-ym-tree-department1");
            } else if ("company".equals(entity1.getType())) {
                entity1.setIcon("icon-ym icon-ym-tree-organization3");
            }
            OrganizeEntity organizeEntity = organizeService.getInfo(entity1.getId());
            if (organizeEntity != null) {
                entity1.setOrganize(organizeService.getFullNameByOrgIdTree(organizeEntity.getOrganizeIdTree(), "/"));
                entity1.setOrganizeIds(organizeService.getOrgIdTree(organizeEntity));
            } else {
                entity1.setOrganizeIds(new ArrayList<>());
            }
            entity1.setOrganizeIds(new ArrayList<>());
        }
        JSONArray objects = ListToTreeUtil.treeWhere(posList, orgList);
        List<PosOrgModel> jsonToList = JsonUtil.getJsonToList(objects, PosOrgModel.class);

        List<PosOrgModel> list = new ArrayList<>(16);
        // 得到角色的值
        List<PosOrgModel> collect = jsonToList.stream().filter(t -> "position".equals(t.getType())).sorted(Comparator.comparing(PosOrgModel::getSortCode)).collect(Collectors.toList());
        list.addAll(collect);
        jsonToList.removeAll(collect);
        List<PosOrgModel> collect1 = jsonToList.stream().sorted(Comparator.comparing(PosOrgModel::getSortCode).thenComparing(PosOrgModel::getCreatorTime, Comparator.reverseOrder())).collect(Collectors.toList());
        list.addAll(collect1);

        List<SumTree<PosOrgModel>> trees = TreeDotUtils.convertListToTreeDot(list);
        List<PositionSelectorVO> jsonToList1 = JsonUtil.getJsonToList(trees, PositionSelectorVO.class);
        ListVO vo = new ListVO();
        vo.setList(jsonToList1);
        return ActionResult.success(vo);
    }

    /**
     * 通过部门、岗位获取岗位下拉框
     *
     * @param positionConditionModel 岗位选择模型
     * @return
     */
    @Operation(summary = "通过部门、岗位获取岗位下拉框")
    @Parameters({
            @Parameter(name = "positionConditionModel", description = "岗位选择模型", required = true)
    })
    @PostMapping("/PositionCondition")
    public ActionResult<ListVO<PositionConditionSelectorVO>> positionCondition(@RequestBody PositionConditionModel positionConditionModel) {
        // 定义返回对象
        List<PositionConditionSelectorVO> modelList = new ArrayList<>();
        List<String> list = new ArrayList<>(16);
        if (positionConditionModel.getDepartIds() != null) {
            list.addAll(positionConditionModel.getDepartIds());
        }
        // 获取岗位关系
        List<String> collect = organizeRelationService.getRelationListByOrganizeId(list, PermissionConst.POSITION).stream().map(OrganizeRelationEntity::getObjectId).collect(Collectors.toList());
        if (positionConditionModel.getPositionIds() != null) {
            collect.addAll(positionConditionModel.getPositionIds());
        }
        collect = collect.stream().distinct().collect(Collectors.toList());
        // 岗位信息
        List<PositionEntity> positionName = positionService.getPositionName(collect, positionConditionModel.getKeyword());
        positionName = positionName.stream().filter(t -> "1".equals(String.valueOf(t.getEnabledMark()))).collect(Collectors.toList());
        List<PosOrgConditionModel> posOrgModels = new ArrayList<>(16);
        positionName.forEach(t -> {
            PosOrgConditionModel posOrgModel = JsonUtil.getJsonToBean(t, PosOrgConditionModel.class);
            posOrgModel.setOrganizeId(t.getOrganizeId());
            posOrgModel.setParentId(t.getOrganizeId());
            posOrgModel.setType("position");
            posOrgModel.setIcon("icon-ym icon-ym-tree-position1");
            posOrgModels.add(posOrgModel);
        });

        // 处理组织
        // 得到组织的id
        List<String> collect2 = posOrgModels.stream().map(PosOrgConditionModel::getOrganizeId).distinct().collect(Collectors.toList());
        // 得到所有的组织信息
        List<OrganizeEntity> organizeName = organizeService.getOrganizeName(collect2);
        organizeName.forEach(org -> {
            PosOrgConditionModel orgVo = JsonUtil.getJsonToBean(org, PosOrgConditionModel.class);
            if ("department".equals(orgVo.getType())) {
                orgVo.setIcon("icon-ym icon-ym-tree-department1");
            } else if ("company".equals(orgVo.getType())) {
                orgVo.setIcon("icon-ym icon-ym-tree-organization3");
            }
            // 处理断层
            if (StringUtil.isNotEmpty(org.getOrganizeIdTree())) {
                List<String> list1 = new ArrayList<>();
                String[] split = org.getOrganizeIdTree().split(",");
                list1 = Arrays.asList(split);
                Collections.reverse(list1);
                for (String orgId : list1) {
                    OrganizeEntity organizeEntity1 = organizeName.stream().filter(organizeEntity -> organizeEntity.getId().equals(orgId)).findFirst().orElse(null);
                    if (organizeEntity1 != null && !organizeEntity1.getId().equals(orgVo.getId())) {
                        orgVo.setParentId(organizeEntity1.getId());
                        String[] split1 = org.getOrganizeIdTree().split(organizeEntity1.getId());
                        if (split1.length > 1) {
                            orgVo.setFullName(organizeService.getFullNameByOrgIdTree(split1[1], "/"));
                        }
                        break;
                    }
                }
            }
            posOrgModels.add(orgVo);
        });

        List<SumTree<PosOrgConditionModel>> trees = TreeDotUtils.convertListToTreeDot(posOrgModels);
        List<PositionConditionSelectorVO> positionSelectorVO = JsonUtil.getJsonToList(trees, PositionConditionSelectorVO.class);
        // 处理数据
        positionSelectorVO.forEach(t -> {
            if (!"position".equals(t.getType())) {
                t.setFullName(organizeService.getFullNameByOrgIdTree(t.getOrganizeIdTree(), "/"));
            }
        });
        modelList.addAll(positionSelectorVO);
        ListVO vo = new ListVO();
        vo.setList(modelList);
        return ActionResult.success(vo);
    }

    /**
     * 通过组织id获取岗位列表
     *
     * @param organizeId 主键值
     * @return
     */
    @Operation(summary = "通过组织id获取岗位列表")
    @Parameters({
            @Parameter(name = "organizeId", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.position")
    @GetMapping("/getList/{organizeId}")
    public ActionResult<List<PositionVo>> getListByOrganizeId(@PathVariable("organizeId") String organizeId) {
        List<PositionEntity> list = positionService.getListByOrganizeId(organizeId);
        List<PositionVo> jsonToList = JsonUtil.getJsonToList(list, PositionVo.class);
        return ActionResult.success(jsonToList);
    }

    /**
     * 获取岗位管理信息
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "获取岗位管理信息")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.position")
    @GetMapping("/{id}")
    public ActionResult<PositionInfoVO> getInfo(@PathVariable("id") String id) throws DataException {
        PositionEntity entity = positionService.getInfo(id);
        PositionInfoVO vo = JsonUtilEx.getJsonToBeanEx(entity, PositionInfoVO.class);
        String organizeId = entity.getOrganizeId();
        OrganizeEntity organizeEntity = organizeService.getInfo(organizeId);
        vo.setOrganizeIdTree(StringUtil.isNotEmpty(organizeEntity.getOrganizeIdTree()) ? Arrays.asList(organizeEntity.getOrganizeIdTree().split(",")) : new ArrayList<>());
        return ActionResult.success(vo);
    }


    /**
     * 新建岗位管理
     *
     * @param positionCrForm 实体对象
     * @return
     */
    @PositionPermission
    @Operation(summary = "新建岗位管理")
    @Parameters({
            @Parameter(name = "positionCrForm", description = "实体对象", required = true)
    })
    @SaCheckPermission("permission.position")
    @PostMapping
    public ActionResult create(@RequestBody @Valid PositionCrForm positionCrForm) {
        PositionEntity entity = JsonUtil.getJsonToBean(positionCrForm, PositionEntity.class);
        if (positionService.isExistByFullName(entity, false)) {
            return ActionResult.fail("岗位名称不能重复");
        }
        if (positionService.isExistByEnCode(entity, false)) {
            return ActionResult.fail("岗位编码不能重复");
        }
        // 设置岗位id
        entity.setId(RandomUtil.uuId());
        createOrganizeRoleRelation(entity.getOrganizeId(), entity.getId());
        positionService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新岗位管理
     *
     * @param id             主键值
     * @param positionUpForm 实体对象
     * @return
     */
    @PositionPermission
    @Operation(summary = "更新岗位管理")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true),
            @Parameter(name = "positionUpForm", description = "实体对象", required = true)
    })
    @SaCheckPermission("permission.position")
    @PutMapping("/{id}")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid PositionUpForm positionUpForm) {
        // 当岗位绑定用户不让其更改
        if(userRelationService.existByObj(PermissionConst.POSITION, id)){
            if(!positionService.getInfo(id).getOrganizeId().equals(positionUpForm.getOrganizeId())){
                return ActionResult.fail(MsgCode.FA023.get());
            }
        }
        PositionEntity entity = JsonUtil.getJsonToBean(positionUpForm, PositionEntity.class);
        entity.setId(id);
        if (positionService.isExistByFullName(entity, true)) {
            return ActionResult.fail("岗位名称不能重复");
        }
        if (positionService.isExistByEnCode(entity, true)) {
            return ActionResult.fail("岗位编码不能重复");
        }
        createOrganizeRoleRelation(entity.getOrganizeId(), id);
        boolean flag = positionService.update(id, entity);
        if (flag == false) {
            return ActionResult.fail(MsgCode.FA002.get());
        }
        return ActionResult.success(MsgCode.SU004.get());
    }

    /**
     * 删除岗位管理
     *
     * @param id 主键值
     * @return
     */
    @PositionPermission
    @Operation(summary = "删除岗位管理")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.position")
    @DeleteMapping("/{id}")
    public ActionResult delete(@PathVariable("id") String id) {
        // 当岗位绑定用户不让其更改
        if(userRelationService.existByObj(PermissionConst.POSITION, id)){
            return ActionResult.fail(MsgCode.FA024.get());
        }
        PositionEntity entity = positionService.getInfo(id);
        if (entity != null) {
            List<UserRelationEntity> userRelList = userRelationService.getListByObjectId(id);
            if(userRelList.size()>0){
                return ActionResult.
                        fail("该岗位下有用户");
            }
            for (UserRelationEntity entity1 : userRelList) {
                UserEntity entity2 = userService.getById(entity1.getUserId());
                if (entity2 != null) {
                    String newPositionId = entity2.getPositionId().replace(id, "");
                    if (entity2.getPositionId().contains(id)) {
                        if (newPositionId.length() != 0 && newPositionId.substring(0, 1).equals(",")) {
                            entity2.setPositionId(newPositionId.substring(1));
                        } else if (newPositionId.length() != 0) {
                            entity2.setPositionId(newPositionId.replace(",,", ","));
                        }
                    }
                }
            }
            userRelationService.deleteAllByObjId(id);

            // 删除岗位与组织之间的关联数据
            QueryWrapper<OrganizeRelationEntity> query = new QueryWrapper<>();
            query.lambda().eq(OrganizeRelationEntity::getObjectType, PermissionConst.POSITION);
            query.lambda().eq(OrganizeRelationEntity::getObjectId, id);
            organizeRelationService.remove(query);

            positionService.delete(entity);
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.fail(MsgCode.FA003.get());
    }

    /**
     * 更新菜单状态
     *
     * @param id 主键值
     * @return
     */
    @Operation(summary = "更新菜单状态")
    @Parameters({
            @Parameter(name = "id", description = "主键值", required = true)
    })
    @SaCheckPermission("permission.position")
    @PutMapping("/{id}/Actions/State")
    public ActionResult upState(@PathVariable("id") String id) {
        PositionEntity entity = positionService.getInfo(id);
        if (entity != null) {
            if (entity.getEnabledMark() == null ||"1".equals(String.valueOf(entity.getEnabledMark()))) {
                entity.setEnabledMark(0);
            } else {
                entity.setEnabledMark(1);
            }
            positionService.update(id, entity);
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.fail("MsgCode.UPDATE_FAIL_NOT_EXISTS");
    }

    /**
     * 通过组织id获取岗位列表
     *
     * @param organizeIds 组织id数组
     * @return 岗位列表
     */
    @Operation(summary = "获取岗位列表通过组织id数组")
    @Parameters({
            @Parameter(name = "organizeIds", description = "组织id数组", required = true)
    })
    @SaCheckPermission("permission.user")
    @PostMapping("/getListByOrgIds")
    public ActionResult<ListVO<PermissionModel>> getListByOrganizeIds(@RequestBody @Valid Map<String,List<String>> organizeIds) {
        List<PermissionModel> PositionModelAll = new LinkedList<>();
        for(String organizeId : organizeIds.get("organizeIds")){
            OrganizeEntity info = organizeService.getInfo(organizeId);
            if(info != null){
                PermissionModel parentModel = new PermissionModel();
                List<PositionEntity> list = positionService.getListByOrganizeId(organizeId);
                List<PermissionModel> positionModels = JsonUtil.getJsonToList(list, PermissionModel.class);
                parentModel.setHasChildren(true);
                parentModel.setFullName(info.getFullName());
                parentModel.setId(info.getId());
                parentModel.setChildren(positionModels);
                PositionModelAll.add(parentModel);
            }
        }
        ListVO vo = new ListVO();
        vo.setList(PositionModelAll);
        return ActionResult.success(vo);


    }

    /**
     * 添加组织角色关联关系
     *
     * @param organizeId    组织id
     * @param positionId    岗位id
     */
    private void createOrganizeRoleRelation(String organizeId, String positionId) {
        // 清除之前的关联关系
        QueryWrapper<OrganizeRelationEntity> query = new QueryWrapper<>();
        query.lambda().eq(OrganizeRelationEntity::getObjectType, PermissionConst.POSITION);
        query.lambda().eq(OrganizeRelationEntity::getObjectId, positionId);
        organizeRelationService.remove(query);
        // 添加与组织的关联关系
        OrganizeRelationEntity organizeRelationEntity = new OrganizeRelationEntity();
        organizeRelationEntity.setId(RandomUtil.uuId());
        organizeRelationEntity.setOrganizeId(organizeId);
        organizeRelationEntity.setObjectType(PermissionConst.POSITION);
        organizeRelationEntity.setObjectId(positionId);
        organizeRelationService.save(organizeRelationEntity);
    }


}
