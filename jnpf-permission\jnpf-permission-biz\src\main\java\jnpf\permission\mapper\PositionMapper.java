package jnpf.permission.mapper;

import jnpf.base.mapper.SuperMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import jnpf.permission.entity.PositionEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 岗位信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
public interface PositionMapper extends SuperMapper<PositionEntity> {

    /**
     * 通过组织id获取用户信息
     *
     * @param orgId
     * @param orgIdList
     * @return
     */
    List<String> query(@Param("orgId") String orgId, @Param("orgIdList") List<String> orgIdList, @Param("keyword") String keyword);
}
