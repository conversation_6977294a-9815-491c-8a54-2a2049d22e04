package jnpf.message.util;

import jnpf.config.ConfigValueUtil;
import jnpf.util.StringUtil;
import jnpf.util.context.SpringContext;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/16 8:47
 */
public class Base64Util {

    private static ConfigValueUtil configValueUtil = SpringContext.getBean(ConfigValueUtil.class);


    /**
     * 把文件转化为base64.
     *
     * @param filePath 源文件路径
     */
    public static String fileToBase64(String filePath) {
        if (!StringUtil.isEmpty(filePath)) {
            try {
                byte[] bytes = Files.readAllBytes(Paths.get(filePath));
                return Base64.encodeBase64String(bytes);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
