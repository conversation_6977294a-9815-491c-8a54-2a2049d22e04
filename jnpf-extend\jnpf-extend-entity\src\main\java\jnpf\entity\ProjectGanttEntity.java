package jnpf.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目计划
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2019年9月26日 上午9:18
 */
@Data
@TableName("ext_projectgantt")
public class ProjectGanttEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    /**
     * 项目上级
     */
    @TableField("F_PARENTID")
    private String parentId;

    /**
     * 项目主键
     */
    @TableField("F_PROJECTID")
    private String projectId;

    /**
     * 项目类型
     */
    @TableField("F_TYPE")
    private Integer type;

    /**
     * 项目编码
     */
    @TableField("F_ENCODE")
    private String enCode;

    /**
     * 项目名称
     */
    @TableField("F_FULLNAME")
    private String fullName;

    /**
     * 项目工期
     */
    @TableField("F_TIMELIMIT")
    private BigDecimal timeLimit;

    /**
     * 项目标记
     */
    @TableField("F_SIGN")
    private String sign;

    /**
     * 标记颜色
     */
    @TableField("F_SIGNCOLOR")
    private String signColor;

    /**
     * 开始时间
     */
    @TableField("F_STARTTIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("F_ENDTIME")
    private Date endTime;

    /**
     * 当前进度
     */
    @TableField("F_SCHEDULE")
    private Integer schedule;

    /**
     * 负责人
     */
    @TableField("F_MANAGERIDS")
    private String managerIds;

    /**
     * 项目状态(1-进行中，2-已暂停)
     */
    @TableField("F_State")
    private Integer state;

}
