package jnpf.entity;

import jnpf.base.entity.SuperExtendEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 职员信息
 *
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 *
 */
@Data
@TableName("ext_employee")
public class EmployeeEntity extends SuperExtendEntity.SuperExtendSortEntity<String> {

    /**
     * 工号
     */
    @TableField("F_ENCODE")
    private String enCode;

    /**
     * 姓名
     */
    @TableField("F_FULLNAME")
    private String fullName;

    /**
     * 性别
     */
    @TableField("F_GENDER")
    private String gender;

    /**
     * 部门
     */
    @TableField("F_DEPARTMENTNAME")
    private String departmentName;

    /**
     * 岗位
     */
    @TableField("F_POSITIONNAME")
    private String positionName;

    /**
     * 用工性质
     */
    @TableField("F_WORKINGNATURE")
    private String workingNature;

    /**
     * 身份证号
     */
    @TableField("F_IDNUMBER")
    private String idNumber;

    /**
     * 联系电话
     */
    @TableField("F_TELEPHONE")
    private String telephone;

    /**
     * 参加工作
     */
    @TableField("F_ATTENDWORKTIME")
    private Date attendWorkTime;

    /**
     * 出生年月
     */
    @TableField("F_BIRTHDAY")
    private Date birthday;

    /**
     * 最高学历
     */
    @TableField("F_EDUCATION")
    private String education;

    /**
     * 所学专业
     */
    @TableField("F_MAJOR")
    private String major;

    /**
     * 毕业院校
     */
    @TableField("F_GRADUATIONACADEMY")
    private String graduationAcademy;

    /**
     * 毕业时间
     */
    @TableField("F_GRADUATIONTIME")
    private Date graduationTime;

}
