package jnpf.entity;

import jnpf.base.entity.SuperEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 产品商品
 *
 * @版本： V3.1.0
 * @版权： 引迈信息技术有限公司（https://www.jnpfsoft.com）
 * @作者： JNPF开发平台组
 * @日期： 2021-07-10 15:57:50
 */
@Data
@TableName("ext_productgoods")
public class ProductgoodsEntity extends SuperEntity<String> {

    /**
     * 分类主键
     */
    @TableField("F_CLASSIFYID")
    private String classifyId;

    /**
     * 产品编号
     */
    @TableField("F_CODE")
    private String code;

    /**
     * 产品名称
     */
    @TableField("F_FULLNAME")
    private String fullName;

    /**
     * 订货类型
     */
    @TableField("F_TYPE")
    private String type;

    /** 产品规格 */
    @TableField("F_PRODUCTSPECIFICATION")
    private String productSpecification;

    /** 单价 */
    @TableField("F_MONEY")
    private String money;

    /**
     * 库存数
     */
    @TableField("F_QTY")
    private Integer qty;

    /** 金额 */
    @TableField("F_AMOUNT")
    private String amount;

}
