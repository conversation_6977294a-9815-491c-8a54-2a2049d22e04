package com.ruoyi.ffsafe.scantaskapi.service.impl;

import com.ruoyi.ffsafe.api.domain.FfsafeFlowDetail;
import com.ruoyi.ffsafe.scantaskapi.domain.*;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.ITaskDetailResultService;
import com.ruoyi.ffsafe.scantaskapi.service.mapper.*;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssVulnInfo;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWpDeal;
import com.ruoyi.monitor2.mapper.MonitorBssVulnDealMapper;
import com.ruoyi.monitor2.mapper.MonitorBssVulnInfoMapper;
import com.ruoyi.monitor2.mapper.MonitorBssWebvulnDealMapper;
import com.ruoyi.monitor2.mapper.MonitorBssWpDealMapper;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.mapper.SysJobMapper;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.TblBusinessApplication;
import com.ruoyi.safe.domain.TblDeductionDetail;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.domain.TblThreatDeductionStandard;
import com.ruoyi.safe.mapper.TblBusinessApplicationMapper;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.safe.service.ITblDeductionDetailService;
import com.ruoyi.safe.service.ITblThreatDeductionStandardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class TaskDetailResultServiceImpl implements ITaskDetailResultService {

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private ITblDeductionDetailService tblDeductionDetailService;

    @Resource
    private TblNetworkIpMacMapper tblNetworkIpMacMapper;

    @Autowired
    private ITblThreatDeductionStandardService tblThreatDeductionStandardService;

    @Resource
    private TblBusinessApplicationMapper tblBusinessApplicationMapper;

    @Autowired
    private IFfsafeScantaskSummaryService ffsafeScantaskSummaryService;
    @Resource
    private IHandleDataSyncSender handleDataSyncSender;

    private List<FfsafeHostscanTaskResult> getFfsafeHostscanTaskList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult) {
        List<FfsafeHostscanTaskResult> hostscanTaskResultList = new ArrayList<FfsafeHostscanTaskResult>();
        List<HostScanTaskDetailResult.Task> taskList = hostScanTaskDetailResult.getTaskList();
        for (HostScanTaskDetailResult.Task taskInfo: taskList) {
            FfsafeHostscanTaskResult ffsafeHostscanTaskResult = taskInfo.toHostScanTaskResult(taskId);
            hostscanTaskResultList.add(ffsafeHostscanTaskResult);
        }

        return hostscanTaskResultList;
    }

    private List<FfsafeHostscanVulnResult> getFfsafeHostscanVulnList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult) {
        List<FfsafeHostscanVulnResult> hostscanVulnResultList = new ArrayList<FfsafeHostscanVulnResult>();
        List<HostScanTaskDetailResult.Expoloit> expoloitList = hostScanTaskDetailResult.getExpoloitList();
        for (HostScanTaskDetailResult.Expoloit expoloitInfo: expoloitList) {
            List<FfsafeHostscanVulnResult> tempVulnList = expoloitInfo.toHostScanTaskVulnList(taskId);
            hostscanVulnResultList.addAll(tempVulnList);
        }

        return hostscanVulnResultList;
    }

    private List<FfsafeHostscanPortResult> getFfsafeHostscanPortList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult) {
        List<FfsafeHostscanPortResult> hostscanPortResultList = new ArrayList<FfsafeHostscanPortResult>();
        List<HostScanTaskDetailResult.Port> portList = hostScanTaskDetailResult.getPortList();
        for (HostScanTaskDetailResult.Port portInfo: portList) {
            FfsafeHostscanPortResult ffsafeHostscanPortResult = portInfo.toHostScanTaskPort(taskId);
            hostscanPortResultList.add(ffsafeHostscanPortResult);
        }

        return hostscanPortResultList;
    }

    private List<FfsafeHostscanWpresult> getFfsafeHostscanWpresultList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult) {
        List<FfsafeHostscanWpresult> ffsafeHostscanWpresultList = new ArrayList<FfsafeHostscanWpresult>();
        List<HostScanTaskDetailResult.WeakPassword> weakPasswordList = hostScanTaskDetailResult.getWeakPasswordList();
        for (HostScanTaskDetailResult.WeakPassword weakPasswordInfo: weakPasswordList) {
            FfsafeHostscanWpresult ffsafeHostscanWpresult = weakPasswordInfo.toHostScanTaskWp(taskId);
            ffsafeHostscanWpresultList.add(ffsafeHostscanWpresult);
        }

        return ffsafeHostscanWpresultList;
    }

    private Map<String, MonitorBssVulnInfo> getMonitorBssVulnInfoMap(HostScanTaskDetailResult hostScanTaskDetailResult) {
        Map<String, MonitorBssVulnInfo> bssVulnInfoMap = new HashMap<String, MonitorBssVulnInfo>();

        List<HostScanTaskDetailResult.Expoloit> expoloitList = hostScanTaskDetailResult.getExpoloitList();
        for (HostScanTaskDetailResult.Expoloit expoloitInfo: expoloitList) {
            MonitorBssVulnInfo tempVulnInfo = expoloitInfo.toMonitorBssVulnInfo();
            if (bssVulnInfoMap.get(tempVulnInfo.getTitle()) == null) {
                bssVulnInfoMap.put(tempVulnInfo.getTitle(), tempVulnInfo);
            }
        }

        return bssVulnInfoMap;
    }

    private TblNetworkIpMac getAssetInfoByIp(String ip) {
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setIpv4(ip);
        tblNetworkIpMac.setMainIp("1");  // 表示主ip
        List<TblNetworkIpMac> tblNetworkIpMacList = tblNetworkIpMacMapper.selectTblNetworkIpMacList(tblNetworkIpMac);
        if ((tblNetworkIpMacList != null) && (tblNetworkIpMacList.size() > 0)) {
            return tblNetworkIpMacList.get(0);
        }

        return null;
    }

    private List<MonitorBssWpDeal> getMonitorBssWpDealList(List<FfsafeHostscanWpresult> ffsafeHostscanWpresultList) {
        List<MonitorBssWpDeal> monitorBssWpDealList = new ArrayList<MonitorBssWpDeal>();

        for (FfsafeHostscanWpresult ffsafeHostscanWpresult: ffsafeHostscanWpresultList) {
            MonitorBssWpDeal monitorBssWpDeal = new MonitorBssWpDeal();
            TblNetworkIpMac tblNetworkIpMac = getAssetInfoByIp(ffsafeHostscanWpresult.getHostIp());
            if (tblNetworkIpMac != null) {
                monitorBssWpDeal.setAssetId(tblNetworkIpMac.getAssetId());
                monitorBssWpDeal.setDeptId(tblNetworkIpMac.getDeptId());
            }
            monitorBssWpDeal.setNum(1);
            monitorBssWpDeal.setHostIp(ffsafeHostscanWpresult.getHostIp());
            monitorBssWpDeal.setHostPort(ffsafeHostscanWpresult.getHostPort());
            monitorBssWpDeal.setServiceType(ffsafeHostscanWpresult.getServiceType());
            monitorBssWpDeal.setUsername(ffsafeHostscanWpresult.getUsername());
            monitorBssWpDeal.setWeakPassword(ffsafeHostscanWpresult.getWeakPassword());
            monitorBssWpDeal.setCreateTime(ffsafeHostscanWpresult.getCreateTime());
            monitorBssWpDeal.setUpdateTime(ffsafeHostscanWpresult.getCreateTime());

            monitorBssWpDealList.add(monitorBssWpDeal);
        }

        return monitorBssWpDealList;
    }

    private List<MonitorBssVulnDeal> getMonitorBssVulnDealList(List<FfsafeHostscanVulnResult> ffsafeHostscanVulnResultList) {
        List<MonitorBssVulnDeal> monitorBssVulnDealList = new ArrayList<MonitorBssVulnDeal>();
        for (FfsafeHostscanVulnResult ffsafeHostscanVulnResult: ffsafeHostscanVulnResultList) {
            MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
            TblNetworkIpMac tblNetworkIpMac = getAssetInfoByIp(ffsafeHostscanVulnResult.getHostIp());
            if (tblNetworkIpMac != null) {
                monitorBssVulnDeal.setAssetId(tblNetworkIpMac.getAssetId());
                monitorBssVulnDeal.setDeptId(tblNetworkIpMac.getDeptId());
            }
            monitorBssVulnDeal.setScanNum(1);
            monitorBssVulnDeal.setHostIp(ffsafeHostscanVulnResult.getHostIp());
            monitorBssVulnDeal.setDataSource(1);
            monitorBssVulnDeal.setTitle(ffsafeHostscanVulnResult.getVulnName());
            monitorBssVulnDeal.setCategory(ffsafeHostscanVulnResult.getVulnType());
            monitorBssVulnDeal.setCreateTime(ffsafeHostscanVulnResult.getCreateTime());
            monitorBssVulnDeal.setUpdateTime(ffsafeHostscanVulnResult.getCreateTime());
            monitorBssVulnDeal.setSeverity(ffsafeHostscanVulnResult.getRiskLevel());
            monitorBssVulnDeal.setHostPort(ffsafeHostscanVulnResult.getHostPort());
            monitorBssVulnDealList.add(monitorBssVulnDeal);
        }

        return monitorBssVulnDealList;
    }


    private List<TblNetworkIpMac> getTblNetworkIpMacList(HostScanTaskSummaryResult hostScanTaskSummaryResult, List<MonitorBssVulnDeal> monitorBssVulnDealList) {
        Map<Long, TblNetworkIpMac> tblNetworkIpMacMap = new HashMap<Long, TblNetworkIpMac>();
        for (MonitorBssVulnDeal monitorBssVulnDeal: monitorBssVulnDealList) {
            if (monitorBssVulnDeal.getAssetId() != null) {
                TblNetworkIpMac temp = tblNetworkIpMacMap.get(monitorBssVulnDeal.getAssetId());
                if (temp == null) {
                    TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
                    tblNetworkIpMac.setAssetId(monitorBssVulnDeal.getAssetId());
                    tblNetworkIpMac.setVulnNum(1);
                    tblNetworkIpMac.setVulnUpdateTime(hostScanTaskSummaryResult.toFfsafeScantaskSummary().getEndTime());
                    tblNetworkIpMacMap.put(monitorBssVulnDeal.getAssetId(), tblNetworkIpMac);
                } else {
                    temp.setVulnNum(temp.getVulnNum() + 1);
                    tblNetworkIpMacMap.put(monitorBssVulnDeal.getAssetId(), temp);
                }
            }
        }

        return  new ArrayList<TblNetworkIpMac>(tblNetworkIpMacMap.values());
    }

    @Override
    public boolean dealTaskDetailResult(int taskId, HostScanTaskSummaryResult hostScanTaskSummaryResult, HostScanTaskDetailResult hostScanTaskDetailResult, SysJob sysJob) {
        boolean bRet = false;
        SqlSession sqlSession = null;
        List<FfsafeHostscanTaskResult> ffsafeHostscanTaskResultList = getFfsafeHostscanTaskList(taskId, hostScanTaskDetailResult);
        List<FfsafeHostscanVulnResult> ffsafeHostscanVulnResultList = getFfsafeHostscanVulnList(taskId, hostScanTaskDetailResult);
        List<FfsafeHostscanPortResult> ffsafeHostscanPortResultList = getFfsafeHostscanPortList(taskId, hostScanTaskDetailResult);
        List<FfsafeHostscanWpresult> ffsafeHostscanWpresultList = getFfsafeHostscanWpresultList(taskId, hostScanTaskDetailResult);
        List<MonitorBssVulnDeal> monitorBssVulnDealList = getMonitorBssVulnDealList(ffsafeHostscanVulnResultList);
        List<MonitorBssWpDeal> monitorBssWpDealList = getMonitorBssWpDealList(ffsafeHostscanWpresultList);
        List<TblNetworkIpMac> tblNetworkIpMacList = getTblNetworkIpMacList(hostScanTaskSummaryResult, monitorBssVulnDealList);

        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeHostscanTaskResultMapper hostscanTaskResultMapper = sqlSession.getMapper(FfsafeHostscanTaskResultMapper.class);
            FfsafeHostscanVulnResultMapper hostscanVulnResultMapper = sqlSession.getMapper(FfsafeHostscanVulnResultMapper.class);
            FfsafeHostscanPortResultMapper hostscanPortResultMapper = sqlSession.getMapper(FfsafeHostscanPortResultMapper.class);
            FfsafeHostscanWpresultMapper hostscanWpresultMapper = sqlSession.getMapper(FfsafeHostscanWpresultMapper.class);
            SysJobMapper sysJobMapper = sqlSession.getMapper(SysJobMapper.class);
            MonitorBssVulnInfoMapper monitorBssVulnInfoMapper = sqlSession.getMapper(MonitorBssVulnInfoMapper.class);
            MonitorBssVulnDealMapper monitorBssVulnDealMapper = sqlSession.getMapper(MonitorBssVulnDealMapper.class);
            MonitorBssWpDealMapper monitorBssWpDealMapper = sqlSession.getMapper(MonitorBssWpDealMapper.class);
            FfsafeScantaskSummaryMapper hostscanTaskSummerayMapper = sqlSession.getMapper(FfsafeScantaskSummaryMapper.class);
            TblNetworkIpMacMapper networkIpMacMapper = sqlSession.getMapper(TblNetworkIpMacMapper.class);    // 此表为资产关联表


            ffsafeHostscanTaskResultList.stream().forEach(result -> hostscanTaskResultMapper.insertFfsafeHostscanTaskresult(result));
            ffsafeHostscanVulnResultList.stream().forEach(result -> hostscanVulnResultMapper.insertFfsafeHostscanVulnResult(result));
            ffsafeHostscanPortResultList.stream().forEach(result -> hostscanPortResultMapper.insertFfsafeHostscanPortresult(result));
            ffsafeHostscanWpresultList.stream().forEach(result -> hostscanWpresultMapper.insertFfsafeHostscanWpresult(result));

            List<MonitorBssVulnInfo> saveMonitorBssVulnInfoList = new ArrayList<MonitorBssVulnInfo>();
            List<MonitorBssVulnInfo> updateMonitorBssVulnInfoList = new ArrayList<MonitorBssVulnInfo>();
            Map<String, MonitorBssVulnInfo> monitorBssVulnInfoMap = getMonitorBssVulnInfoMap(hostScanTaskDetailResult);
            for (Map.Entry<String, MonitorBssVulnInfo> entry: monitorBssVulnInfoMap.entrySet()) {
                MonitorBssVulnInfo tempInfo = entry.getValue();
                List<MonitorBssVulnInfo> monitorBssVulnInfoList = monitorBssVulnInfoMapper.getMonitorBssVulnInfoList(tempInfo);
                if ((monitorBssVulnInfoList != null)&&(monitorBssVulnInfoList.size() > 0)) {
                    tempInfo.setId(monitorBssVulnInfoList.get(0).getId());
                    monitorBssVulnInfoMapper.updateMonitorBssVulnInfo(tempInfo);
                    if(tempInfo.getSeverity() != null && tempInfo.getSeverity() > 1){
                        updateMonitorBssVulnInfoList.add(tempInfo);
                    }
                } else {
                    monitorBssVulnInfoMapper.insertMonitorBssVulnInfo(tempInfo);
                    if(tempInfo.getSeverity() != null && tempInfo.getSeverity() > 1){
                        saveMonitorBssVulnInfoList.add(tempInfo);
                    }
                }
            }

            List<MonitorBssVulnDeal> saveMonitorBssVulnDealList = new ArrayList<MonitorBssVulnDeal>();
            List<MonitorBssVulnDeal> updateMonitorBssVulnDealList = new ArrayList<MonitorBssVulnDeal>();
            List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardService.selectTblThreatDeductionStandardList(new TblThreatDeductionStandard());
            for (MonitorBssVulnDeal monitorBssVulnDeal: monitorBssVulnDealList) {
                List<MonitorBssVulnDeal> vulnDealList = monitorBssVulnDealMapper.findMonitorBssVulnDealList(monitorBssVulnDeal);
                if ((vulnDealList != null)&&(vulnDealList.size() > 0)) {
                    MonitorBssVulnDeal vulnDeal = vulnDealList.get(0);
                    vulnDeal.setUpdateTime(monitorBssVulnDeal.getUpdateTime());
                    vulnDeal.setScanNum(vulnDeal.getScanNum() + monitorBssVulnDeal.getScanNum());
                    vulnDeal.setAssetId(monitorBssVulnDeal.getAssetId());
                    vulnDeal.setDeptId(monitorBssVulnDeal.getDeptId());
                    monitorBssVulnDealMapper.updateMonitorBssVulnDeal(vulnDeal);
                    if(vulnDeal.getSeverity() != null && vulnDeal.getSeverity() > 1){
                        updateMonitorBssVulnDealList.add(vulnDeal);
                    }
                } else {
                    monitorBssVulnDeal.setHandleState("0");
                    monitorBssVulnDealMapper.insertMonitorBssVulnDeal(monitorBssVulnDeal);
                    // 获取刚插入的ID
                    String id = monitorBssVulnDealMapper.findMonitorBssVulnDealAtLastId();
                    TblDeductionDetail tblDeductionDetailInsert = new TblDeductionDetail();
                    tblDeductionDetailInsert.setDeductionDate(monitorBssVulnDeal.getCreateTime());
                    tblDeductionDetailInsert.setDeductionType("主机漏洞");
                    tblDeductionDetailInsert.setDeductionLevel(monitorBssVulnDeal.getSeverity() != null ? monitorBssVulnDeal.getSeverity().toString() : "");
                    tblDeductionDetailInsert.setRiskType("内部漏洞");
                    tblDeductionDetailInsert.setReferenceId(id);
                    tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetailInsert, tblThreatDeductionStandards);
                    tblDeductionDetailService.insertTblDeductionDetail(tblDeductionDetailInsert);
                    if(monitorBssVulnDeal.getSeverity() != null && monitorBssVulnDeal.getSeverity() > 1){
                        saveMonitorBssVulnDealList.add(monitorBssVulnDeal);
                    }
                }
            }

            List<MonitorBssWpDeal> saveMonitorBssWpDealList = new ArrayList<MonitorBssWpDeal>();
            List<MonitorBssWpDeal> updateMonitorBssWpDealList = new ArrayList<MonitorBssWpDeal>();
            for (MonitorBssWpDeal monitorBssWpDeal: monitorBssWpDealList) {
                List<MonitorBssWpDeal> wpDealList = monitorBssWpDealMapper.findMonitorBssWpDealList(monitorBssWpDeal);
                if ((wpDealList != null)&&(wpDealList.size() > 0)) {
                    MonitorBssWpDeal wpDeal = wpDealList.get(0);
                    wpDeal.setUpdateTime(monitorBssWpDeal.getUpdateTime());
                    wpDeal.setNum(wpDeal.getNum() + monitorBssWpDeal.getNum());
                    wpDeal.setAssetId(monitorBssWpDeal.getAssetId());
                    wpDeal.setDeptId(monitorBssWpDeal.getDeptId());
                    monitorBssWpDealMapper.updateMonitorBssWpDeal(wpDeal);
                    updateMonitorBssWpDealList.add(wpDeal);
                } else {
                    monitorBssWpDeal.setHandleState(0);
                    monitorBssWpDealMapper.insertMonitorBssWpDeal(monitorBssWpDeal);
                    saveMonitorBssWpDealList.add(monitorBssWpDeal);
                }
            }

            for (int i = 0; i < tblNetworkIpMacList.size(); i++) {
                TblNetworkIpMac tblNetworkIpMac = tblNetworkIpMacList.get(i);
                networkIpMacMapper.updateScheduleInfo(tblNetworkIpMac);
            }

            hostscanTaskSummerayMapper.updateFfsafeScantaskSummaryByTaskId(hostScanTaskSummaryResult.toFfsafeScantaskSummary());

            if (sysJob.getCronExpression().equals("* * * * * ?")) {        // 如果是立即扫描把状态设置无效。防止重复调用
                sysJob.setStatus(SysJob.INVALID);
            }
            sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
            sysJobMapper.updateJob(sysJob);

            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            bRet = true;

            //同步到中台
            //info
            saveMonitorBssVulnInfoList.forEach(monitorBssVulnInfo -> {
                SyncMessage<MonitorBssVulnInfo> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_VULN_INFO);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssVulnInfo);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            updateMonitorBssVulnInfoList.forEach(monitorBssVulnInfo -> {
                SyncMessage<MonitorBssVulnInfo> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_VULN_INFO);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssVulnInfo);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            //IP漏洞
            saveMonitorBssVulnDealList.forEach(monitorBssVulnDeal -> {
                SyncMessage<MonitorBssVulnDeal> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_VULN_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssVulnDeal);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            updateMonitorBssVulnDealList.forEach(monitorBssVulnDeal -> {
                SyncMessage<MonitorBssVulnDeal> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_VULN_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssVulnDeal);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            //弱口令
            saveMonitorBssWpDealList.forEach(monitorBssWpDeal -> {
                SyncMessage<MonitorBssWpDeal> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_WP_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssWpDeal);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            updateMonitorBssWpDealList.forEach(monitorBssWpDeal -> {
                SyncMessage<MonitorBssWpDeal> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_WP_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(monitorBssWpDeal);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
        } catch (Exception e) {
            sqlSession.rollback();
            e.printStackTrace();
            log.error("dealTaskDetailResult 入库出错, taskId:  " + taskId + " 原因: " + e.getMessage());
        }

        return bRet;
    }

    @Override
    public boolean dealTaskSummaryResult(FfsafeScantaskSummary ffsafeScantaskSummary) {
        boolean bRet = false;

        try {
            ffsafeScantaskSummaryService.updateFfsafeScantaskSummaryByTaskId(ffsafeScantaskSummary);
            bRet = true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("dealTaskSummaryResult 入库出错: " + e.getMessage());
        }
        return bRet;
    }

    private List<FfsafeWebscanVulnResult> getFfsafeWebscanVulnResultList (int taskId, List<WebscanTaskDetailResult> webscanTaskDetailResult) {
        List<FfsafeWebscanVulnResult> ffsafeWebscanVulnResultList = new ArrayList<FfsafeWebscanVulnResult>();
        for (WebscanTaskDetailResult taskDetailResult : webscanTaskDetailResult) {
            List<FfsafeWebscanVulnResult> tempList = taskDetailResult.toFfsafeWebscanVulnList(taskId);
            ffsafeWebscanVulnResultList.addAll(tempList);
        }

        return ffsafeWebscanVulnResultList;
    }

    private Map<String, MonitorBssVulnInfo> getMonitorBssVulnInfoMap(List<WebscanTaskDetailResult> webscanTaskDetailResultList) {
        Map<String, MonitorBssVulnInfo> bssVulnInfoMap = new HashMap<String, MonitorBssVulnInfo>();

        for (WebscanTaskDetailResult taskDetailResult: webscanTaskDetailResultList) {
            MonitorBssVulnInfo tempVulnInfo = taskDetailResult.toMonitoBssVulnInfo();
            if (bssVulnInfoMap.get(tempVulnInfo.getTitle()) == null) {
                bssVulnInfoMap.put(tempVulnInfo.getTitle(), tempVulnInfo);
            }
        }

        return bssVulnInfoMap;
    }

    private TblBusinessApplication getBusinessApplication(String url) {
        String temp = url.substring(8);     // 去除掉http:// 或者 https://
        int index = temp.indexOf("/");
        String destUrl = url.substring(0, index+8);

        TblBusinessApplication businessApplication = new TblBusinessApplication();
        businessApplication.setUrl(destUrl);
        List<TblBusinessApplication> tblBusinessApplicationList = tblBusinessApplicationMapper.selectTblBusinessApplicationList(businessApplication);
        if ((tblBusinessApplicationList != null) && (tblBusinessApplicationList.size() > 0)) {
            return tblBusinessApplicationList.get(0);
        }

        return null;
    }

    private List<MonitorBssWebvulnDeal> getMonitorBssWebvulnDealList(List<FfsafeWebscanVulnResult> ffsafeWebscanVulnResultList) {
        List<MonitorBssWebvulnDeal> monitorBssWebvulnDealList = new ArrayList<MonitorBssWebvulnDeal>();
        for (FfsafeWebscanVulnResult ffsafeWebscanVulnResult: ffsafeWebscanVulnResultList) {
            MonitorBssWebvulnDeal monitorBssWebvulnDeal = new MonitorBssWebvulnDeal();
            TblBusinessApplication tblBusinessApplication = getBusinessApplication(ffsafeWebscanVulnResult.getUrl());
            if (tblBusinessApplication != null) {
                monitorBssWebvulnDeal.setAssetId(String.valueOf(tblBusinessApplication.getAssetId()));
                monitorBssWebvulnDeal.setDeptId(tblBusinessApplication.getDeptId());
            }
            monitorBssWebvulnDeal.setScanNum(1);
            monitorBssWebvulnDeal.setWebUrl(ffsafeWebscanVulnResult.getUrl());
            monitorBssWebvulnDeal.setDataSource(1);
            monitorBssWebvulnDeal.setTitle(ffsafeWebscanVulnResult.getVulnName());
            monitorBssWebvulnDeal.setCategory(ffsafeWebscanVulnResult.getVulnType());
            monitorBssWebvulnDeal.setCreateTime(ffsafeWebscanVulnResult.getCreateTime());
            monitorBssWebvulnDeal.setUpdateTime(ffsafeWebscanVulnResult.getCreateTime());
            monitorBssWebvulnDeal.setSeverity(ffsafeWebscanVulnResult.getSeverity());
            monitorBssWebvulnDealList.add(monitorBssWebvulnDeal);
        }

        return monitorBssWebvulnDealList;
    }

    @Override
    public boolean dealWebscanTaskDetailResult(int taskId, WebScanTaskSummaryResult webScanTaskSummaryResult, List<WebscanTaskDetailResult> webscanTaskDetailResultList, SysJob sysJob) {
        boolean bRet = false;
        SqlSession sqlSession = null;

        List<FfsafeWebscanVulnResult> ffsafeWebscanVulnResultList = getFfsafeWebscanVulnResultList(taskId, webscanTaskDetailResultList);
        List<MonitorBssWebvulnDeal> monitorBssWebvulnDealList = getMonitorBssWebvulnDealList(ffsafeWebscanVulnResultList);
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeWebscanVulnResultMapper webscanVulnResultMapper = sqlSession.getMapper(FfsafeWebscanVulnResultMapper.class);
            MonitorBssVulnInfoMapper monitorBssVulnInfoMapper = sqlSession.getMapper(MonitorBssVulnInfoMapper.class);
            MonitorBssWebvulnDealMapper monitorBssWebvulnDealMapper = sqlSession.getMapper(MonitorBssWebvulnDealMapper.class);
            SysJobMapper sysJobMapper = sqlSession.getMapper(SysJobMapper.class);
            ffsafeWebscanVulnResultList.stream().forEach(result -> webscanVulnResultMapper.insertFfsafeWebscanVulnResult(result));
            FfsafeScantaskSummaryMapper hostscanTaskSummerayMapper = sqlSession.getMapper(FfsafeScantaskSummaryMapper.class);
            Map<String, MonitorBssVulnInfo> monitorBssVulnInfoMap = getMonitorBssVulnInfoMap(webscanTaskDetailResultList);
            List<MonitorBssVulnInfo> saveMonitorBssVulnInfoList = new ArrayList<MonitorBssVulnInfo>();
            for (Map.Entry<String, MonitorBssVulnInfo> entry: monitorBssVulnInfoMap.entrySet()) {
                MonitorBssVulnInfo tempInfo = entry.getValue();
                List<MonitorBssVulnInfo> monitorBssVulnInfoList = monitorBssVulnInfoMapper.getMonitorBssVulnInfoList(tempInfo);
                if ((monitorBssVulnInfoList != null)&&(monitorBssVulnInfoList.size() > 0)) {
                    tempInfo.setId(monitorBssVulnInfoList.get(0).getId());
                    monitorBssVulnInfoMapper.updateMonitorBssVulnInfo(tempInfo);
                } else {
                    monitorBssVulnInfoMapper.insertMonitorBssVulnInfo(tempInfo);
                }
                if(tempInfo.getSeverity() != null && tempInfo.getSeverity() > 1){
                    saveMonitorBssVulnInfoList.add(tempInfo);
                }
            }

            List<MonitorBssWebvulnDeal> saveMonitorBssVulnDealList = new ArrayList<MonitorBssWebvulnDeal>();
            List<MonitorBssWebvulnDeal> updateMonitorBssWebvulnDealList = new ArrayList<MonitorBssWebvulnDeal>();
            for (MonitorBssWebvulnDeal monitorBssWebvulnDeal: monitorBssWebvulnDealList) {
                List<MonitorBssWebvulnDeal> webvulnDealList = monitorBssWebvulnDealMapper.findMonitorBssWebvulnDealList(monitorBssWebvulnDeal);
                if ((webvulnDealList != null)&&(webvulnDealList.size() > 0)) {
                    MonitorBssWebvulnDeal webvulnDeal = webvulnDealList.get(0);
                    webvulnDeal.setUpdateTime(monitorBssWebvulnDeal.getUpdateTime());
                    webvulnDeal.setScanNum(webvulnDeal.getScanNum() + monitorBssWebvulnDeal.getScanNum());
                    webvulnDeal.setAssetId(monitorBssWebvulnDeal.getAssetId());
                    webvulnDeal.setDeptId(monitorBssWebvulnDeal.getDeptId());
                    monitorBssWebvulnDealMapper.updateMonitorBssWebvulnDeal(webvulnDeal);

                    if(webvulnDeal.getSeverity() != null && webvulnDeal.getSeverity() > 1){
                        updateMonitorBssWebvulnDealList.add(webvulnDeal);
                    }
                } else {
                    monitorBssWebvulnDeal.setHandleState(0);
                    monitorBssWebvulnDealMapper.insertMonitorBssWebvulnDeal(monitorBssWebvulnDeal);
                    //获取刚插入的id
                    String id = monitorBssWebvulnDealMapper.getFindTheFirstMonitorBssWebvulnDealId();
                    List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardService.selectTblThreatDeductionStandardList(new TblThreatDeductionStandard());
                    TblDeductionDetail tblDeductionDetailInsert = new TblDeductionDetail();
                    tblDeductionDetailInsert.setDeductionDate(monitorBssWebvulnDeal.getCreateTime());
                    tblDeductionDetailInsert.setDeductionType("WEB漏洞");
                    tblDeductionDetailInsert.setDeductionLevel(monitorBssWebvulnDeal.getSeverity() != null ? monitorBssWebvulnDeal.getSeverity().toString() : "");
                    tblDeductionDetailInsert.setRiskType("内部漏洞");
                    tblDeductionDetailInsert.setReferenceId(id);
                    tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetailInsert, tblThreatDeductionStandards);
                    tblDeductionDetailService.insertTblDeductionDetail(tblDeductionDetailInsert);
                    if(monitorBssWebvulnDeal.getSeverity() != null && monitorBssWebvulnDeal.getSeverity() > 1){
                        saveMonitorBssVulnDealList.add(monitorBssWebvulnDeal);
                    }
                }
            }

            hostscanTaskSummerayMapper.updateFfsafeScantaskSummaryByTaskId(webScanTaskSummaryResult.toFfsafeScantaskSummary());
            if (sysJob.getCronExpression().equals("* * * * * ?")) {        // 如果是立即扫描把状态设置无效。防止重复调用
                sysJob.setStatus(SysJob.INVALID);
            }
            sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
            sysJobMapper.updateJob(sysJob);
            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            bRet = true;

            //发送到中台
            saveMonitorBssVulnInfoList.forEach(saveItem -> {
                SyncMessage<Object> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_VULN_INFO);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(saveItem);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            saveMonitorBssVulnDealList.forEach(saveItem -> {
                SyncMessage<Object> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_WEBVULN_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(saveItem);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
            updateMonitorBssWebvulnDealList.forEach(updateItem -> {
                SyncMessage<Object> message = new SyncMessage<>();
                message.setDataType(DataTypeEnum.MONITOR_BSS_WEBVULN_DEAL);
                message.setOperationType(OperationTypeEnum.INSERT);
                message.setData(updateItem);
                message.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(message);
            });
        } catch (Exception e) {
            sqlSession.rollback();
            e.printStackTrace();
            log.error("deatWebscanTaskDetailResult 入库出错: " + e.getMessage());
        }

        return bRet;
    }

}
